<!-- *** Query *** -->
<passengersGroup>
    <segmentRepetitionControl>
        <segmentControlDetails>
        <quantity>1</quantity>
        <numberOfUnits>2</numberOfUnits>
        </segmentControlDetails>
    </segmentRepetitionControl>
    <travellersID>
        <travellerDetails>
        <measurementValue>1</measurementValue>
        </travellerDetails>
        <travellerDetails>
        <measurementValue>2</measurementValue>
        </travellerDetails>
    </travellersID>
    <discountPtc>
        <valueQualifier>ADT</valueQualifier>
    </discountPtc>
    </passengersGroup>
    <passengersGroup>
    <segmentRepetitionControl>
        <segmentControlDetails>
        <quantity>2</quantity>
        <numberOfUnits>1</numberOfUnits>
        </segmentControlDetails>
    </segmentRepetitionControl>
    <travellersID>
        <travellerDetails>
        <measurementValue>3</measurementValue>
        </travellerDetails>
    </travellersID>
    <discountPtc>
        <valueQualifier>CNN</valueQualifier>
    </discountPtc>
    </passengersGroup>
    <passengersGroup>
    <segmentRepetitionControl>
        <segmentControlDetails>
        <quantity>3</quantity>
        <numberOfUnits>1</numberOfUnits>
        </segmentControlDetails>
    </segmentRepetitionControl>
    <travellersID>
        <travellerDetails>
        <measurementValue>1</measurementValue>
        </travellerDetails>
    </travellersID>
    <discountPtc>
        <valueQualifier>INF</valueQualifier>
        <fareDetails>
        <qualifier>766</qualifier>
        </fareDetails>
    </discountPtc>
    </passengersGroup>
    <segmentGroup>
    <segmentInformation>
        <flightDate>
        <departureDate>030424</departureDate>
        </flightDate>
        <boardPointDetails>
        <trueLocationId>BOG</trueLocationId>
        </boardPointDetails>
        <offpointDetails>
        <trueLocationId>CDG</trueLocationId>
        </offpointDetails>
        <companyDetails>
        <marketingCompany>AF</marketingCompany>
        </companyDetails>
        <flightIdentification>
        <flightNumber>435</flightNumber>
        <bookingClass>R</bookingClass>
        </flightIdentification>
        <flightTypeDetails>
        <flightIndicator>1</flightIndicator>
        </flightTypeDetails>
        <itemNumber>1</itemNumber>
    </segmentInformation>
    </segmentGroup>
    <segmentGroup>
    <segmentInformation>
        <flightDate>
        <departureDate>040424</departureDate>
        </flightDate>
        <boardPointDetails>
        <trueLocationId>CDG</trueLocationId>
        </boardPointDetails>
        <offpointDetails>
        <trueLocationId>FRA</trueLocationId>
        </offpointDetails>
        <companyDetails>
        <marketingCompany>AF</marketingCompany>
        </companyDetails>
        <flightIdentification>
        <flightNumber>1018</flightNumber>
        <bookingClass>L</bookingClass>
        </flightIdentification>
        <flightTypeDetails>
        <flightIndicator>1</flightIndicator>
        </flightTypeDetails>
        <itemNumber>2</itemNumber>
    </segmentInformation>
    </segmentGroup>
    <segmentGroup>
    <segmentInformation>
        <flightDate>
        <departureDate>180424</departureDate>
        </flightDate>
        <boardPointDetails>
        <trueLocationId>FRA</trueLocationId>
        </boardPointDetails>
        <offpointDetails>
        <trueLocationId>CDG</trueLocationId>
        </offpointDetails>
        <companyDetails>
        <marketingCompany>AF</marketingCompany>
        </companyDetails>
        <flightIdentification>
        <flightNumber>1619</flightNumber>
        <bookingClass>L</bookingClass>
        </flightIdentification>
        <flightTypeDetails>
        <flightIndicator>2</flightIndicator>
        </flightTypeDetails>
        <itemNumber>3</itemNumber>
    </segmentInformation>
    </segmentGroup>
    <segmentGroup>
    <segmentInformation>
        <flightDate>
        <departureDate>180424</departureDate>
        </flightDate>
        <boardPointDetails>
        <trueLocationId>CDG</trueLocationId>
        </boardPointDetails>
        <offpointDetails>
        <trueLocationId>BOG</trueLocationId>
        </offpointDetails>
        <companyDetails>
        <marketingCompany>AF</marketingCompany>
        </companyDetails>
        <flightIdentification>
        <flightNumber>436</flightNumber>
        <bookingClass>R</bookingClass>
        </flightIdentification>
        <flightTypeDetails>
        <flightIndicator>2</flightIndicator>
        </flightTypeDetails>
        <itemNumber>4</itemNumber>
    </segmentInformation>
    </segmentGroup>
    <pricingOptionGroup>
    <pricingOptionKey>
        <pricingOptionKey>VC</pricingOptionKey>
    </pricingOptionKey>
    <carrierInformation>
        <companyIdentification>
        <otherCompany>AF</otherCompany>
        </companyIdentification>
    </carrierInformation>
    </pricingOptionGroup>
    <pricingOptionGroup>
    <pricingOptionKey>
        <pricingOptionKey>RP</pricingOptionKey>
    </pricingOptionKey>
    </pricingOptionGroup>
    <pricingOptionGroup>
    <pricingOptionKey>
        <pricingOptionKey>RU</pricingOptionKey>
    </pricingOptionKey>
    </pricingOptionGroup>
    <pricingOptionGroup>
    <pricingOptionKey>
        <pricingOptionKey>RLO</pricingOptionKey>
    </pricingOptionKey>
    </pricingOptionGroup>
    <pricingOptionGroup>
    <pricingOptionKey>
        <pricingOptionKey>FCO</pricingOptionKey>
    </pricingOptionKey>
    <currency>
        <firstCurrencyDetails>
        <currencyQualifier>FCO</currencyQualifier>
        <currencyIsoCode>USD</currencyIsoCode>
        </firstCurrencyDetails>
    </currency>
    </pricingOptionGroup>
</Fare_InformativePricingWithoutPNR>

*** Reply ***
<Fare_InformativePricingWithoutPNRReply>
    <messageDetails>
    <messageFunctionDetails>
        <businessFunction>1</businessFunction>
        <messageFunction>741</messageFunction>
        <responsibleAgency>1A</responsibleAgency>
    </messageFunctionDetails>
    <responseType>A</responseType>
    </messageDetails>
    <mainGroup>
    <dummySegment></dummySegment>
    <convertionRate>
        <conversionRateDetails>
        <rateType>USR</rateType>
        <pricingAmount>1.00</pricingAmount>
        <dutyTaxFeeType>AR</dutyTaxFeeType>
        </conversionRateDetails>
    </convertionRate>
    <generalIndicatorsGroup>
        <generalIndicators>
        <priceTicketDetails>
            <indicators>I</indicators>
        </priceTicketDetails>
        </generalIndicators>
    </generalIndicatorsGroup>
    <pricingGroupLevelGroup>
        <numberOfPax>
        <segmentControlDetails>
            <quantity>1</quantity>
            <numberOfUnits>2</numberOfUnits>
        </segmentControlDetails>
        </numberOfPax>
        <passengersID>
        <travellerDetails>
            <measurementValue>1</measurementValue>
        </travellerDetails>
        <travellerDetails>
            <measurementValue>2</measurementValue>
        </travellerDetails>
        </passengersID>
        <fareInfoGroup>
        <emptySegment></emptySegment>
        <pricingIndicators>
            <priceTariffType>I</priceTariffType>
            <productDateTimeDetails>
            <departureDate>030424</departureDate>
            </productDateTimeDetails>
            <companyDetails>
            <otherCompany>AF</otherCompany>
            </companyDetails>
        </pricingIndicators>
        <fareAmount>
            <monetaryDetails>
            <typeQualifier>B</typeQualifier>
            <amount>409.00</amount>
            <currency>USD</currency>
            </monetaryDetails>
            <otherMonetaryDetails>
            <typeQualifier>712</typeQualifier>
            <amount>995.30</amount>
            <currency>USD</currency>
            </otherMonetaryDetails>
        </fareAmount>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>4</textSubjectQualifier>
            <informationType>15</informationType>
            </freeTextQualification>
            <freeText>03APR24BOG AF X/PAR AF FRA204.00AF X/PAR AF BOG204.00 1S1.09NUC409.09E</freeText>
            <freeText>ND ROE1.00</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1P1</informationType>
            </freeTextQualification>
            <freeText>NON-REFUNDABLE</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A49</informationType>
            </freeTextQualification>
            <freeText> - DATE OF ORIGIN</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILIES:    (ENTER FQFn FOR DETAILS, FXY FOR UPSELL)</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILY:FC1:1-2:LIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILY:FC2:3-4:LIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>NO BAG INCLUDED FOR AT LEAST ONE FLIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>TICKET STOCK RESTRICTION</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>BG CXR: 2*AF/2*AF</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>PRICED WITH VALIDATING CARRIER AF - REPRICE IF DIFFERENT VC</freeText>
        </textData>
        <offerReferences>
            <offerIdentifier>
            <uniqueOfferReference>SP2P-9175475112759695121-1-OI1-AI1</uniqueOfferReference>
            </offerIdentifier>
        </offerReferences>
        <surchargesGroup>
            <taxesAmount>
            <taxDetails>
                <rate>46.00</rate>
                <countryCode>YQ</countryCode>
                <type>AD</type>
            </taxDetails>
            <taxDetails>
                <rate>10.00</rate>
                <countryCode>YR</countryCode>
                <type>VA</type>
            </taxDetails>
            <taxDetails>
                <rate>240.00</rate>
                <countryCode>YR</countryCode>
                <type>VB</type>
            </taxDetails>
            <taxDetails>
                <rate>46.00</rate>
                <countryCode>CO</countryCode>
                <type>AE</type>
            </taxDetails>
            <taxDetails>
                <rate>28.00</rate>
                <countryCode>DG</countryCode>
                <type>VZ</type>
            </taxDetails>
            <taxDetails>
                <rate>67.00</rate>
                <countryCode>YS</countryCode>
                <type>TR</type>
            </taxDetails>
            <taxDetails>
                <rate>10.80</rate>
                <countryCode>FR</countryCode>
                <type>TI</type>
            </taxDetails>
            <taxDetails>
                <rate>33.90</rate>
                <countryCode>QX</countryCode>
                <type>AP</type>
            </taxDetails>
            <taxDetails>
                <rate>10.70</rate>
                <countryCode>DE</countryCode>
                <type>SE</type>
            </taxDetails>
            <taxDetails>
                <rate>62.10</rate>
                <countryCode>OY</countryCode>
                <type>CB</type>
            </taxDetails>
            <taxDetails>
                <rate>31.80</rate>
                <countryCode>RA</countryCode>
                <type>EB</type>
            </taxDetails>
            </taxesAmount>
        </surchargesGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>030424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>BOG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>CDG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>435</flightNumber>
                <bookingClass>R</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <flightTypeDetails>
                <flightIndicator>T</flightIndicator>
            </flightTypeDetails>
            <itemNumber>1</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>030424</departureDate>
                <arrivalDate>030424</arrivalDate>
            </productDateTimeDetails>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>R</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>R</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>ADT</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>040424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>CDG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>FRA</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>1018</flightNumber>
                <bookingClass>L</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <itemNumber>2</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>040424</departureDate>
                <arrivalDate>040424</arrivalDate>
            </productDateTimeDetails>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>L</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>L</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>ADT</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>180424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>FRA</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>CDG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>1619</flightNumber>
                <bookingClass>L</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <flightTypeDetails>
                <flightIndicator>T</flightIndicator>
            </flightTypeDetails>
            <itemNumber>3</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>180424</departureDate>
                <arrivalDate>180424</arrivalDate>
            </productDateTimeDetails>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>L</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>L</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>ADT</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>180424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>CDG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>BOG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>436</flightNumber>
                <bookingClass>R</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <itemNumber>4</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>180424</departureDate>
                <arrivalDate>180424</arrivalDate>
            </productDateTimeDetails>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>R</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>R</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>ADT</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <fareComponentDetailsGroup>
            <fareComponentID>
            <itemNumberDetails>
                <number>1</number>
                <type>FC</type>
            </itemNumberDetails>
            </fareComponentID>
            <marketFareComponent>
            <boardPointDetails>
                <trueLocationId>BOG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>FRA</trueLocationId>
            </offpointDetails>
            </marketFareComponent>
            <monetaryInformation>
            <monetaryDetails>
                <typeQualifier>TFC</typeQualifier>
                <amount>204.00</amount>
                <currency>NUC</currency>
            </monetaryDetails>
            </monetaryInformation>
            <componentClassInfo>
            <fareBasisDetails>
                <rateTariffClass>RL50TALB</rateTariffClass>
            </fareBasisDetails>
            </componentClassInfo>
            <fareQualifiersDetail>
            <discountDetails>
                <fareQualifier>763</fareQualifier>
            </discountDetails>
            </fareQualifiersDetail>
            <fareFamilyDetails>
            <fareFamilyname>LIGHT</fareFamilyname>
            </fareFamilyDetails>
            <fareFamilyOwner>
            <companyIdentification>
                <otherCompany>AF</otherCompany>
            </companyIdentification>
            </fareFamilyOwner>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>1</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>2</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
        </fareComponentDetailsGroup>
        <fareComponentDetailsGroup>
            <fareComponentID>
            <itemNumberDetails>
                <number>2</number>
                <type>FC</type>
            </itemNumberDetails>
            </fareComponentID>
            <marketFareComponent>
            <boardPointDetails>
                <trueLocationId>FRA</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>BOG</trueLocationId>
            </offpointDetails>
            </marketFareComponent>
            <monetaryInformation>
            <monetaryDetails>
                <typeQualifier>TFC</typeQualifier>
                <amount>204.00</amount>
                <currency>NUC</currency>
            </monetaryDetails>
            </monetaryInformation>
            <componentClassInfo>
            <fareBasisDetails>
                <rateTariffClass>RL50TALB</rateTariffClass>
            </fareBasisDetails>
            </componentClassInfo>
            <fareQualifiersDetail>
            <discountDetails>
                <fareQualifier>763</fareQualifier>
            </discountDetails>
            </fareQualifiersDetail>
            <fareFamilyDetails>
            <fareFamilyname>LIGHT</fareFamilyname>
            </fareFamilyDetails>
            <fareFamilyOwner>
            <companyIdentification>
                <otherCompany>AF</otherCompany>
            </companyIdentification>
            </fareFamilyOwner>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>3</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>4</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
        </fareComponentDetailsGroup>
        </fareInfoGroup>
    </pricingGroupLevelGroup>
    <pricingGroupLevelGroup>
        <numberOfPax>
        <segmentControlDetails>
            <quantity>2</quantity>
            <numberOfUnits>1</numberOfUnits>
        </segmentControlDetails>
        </numberOfPax>
        <passengersID>
        <travellerDetails>
            <measurementValue>3</measurementValue>
        </travellerDetails>
        </passengersID>
        <fareInfoGroup>
        <emptySegment></emptySegment>
        <pricingIndicators>
            <priceTariffType>I</priceTariffType>
            <productDateTimeDetails>
            <departureDate>030424</departureDate>
            </productDateTimeDetails>
            <companyDetails>
            <otherCompany>AF</otherCompany>
            </companyDetails>
        </pricingIndicators>
        <fareAmount>
            <monetaryDetails>
            <typeQualifier>B</typeQualifier>
            <amount>307.00</amount>
            <currency>USD</currency>
            </monetaryDetails>
            <otherMonetaryDetails>
            <typeQualifier>712</typeQualifier>
            <amount>883.60</amount>
            <currency>USD</currency>
            </otherMonetaryDetails>
        </fareAmount>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>4</textSubjectQualifier>
            <informationType>15</informationType>
            </freeTextQualification>
            <freeText>03APR24BOG AF X/PAR AF FRA153.00AF X/PAR AF BOG153.00 1S1.09NUC307.09E</freeText>
            <freeText>ND ROE1.00</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1P1</informationType>
            </freeTextQualification>
            <freeText>NON-REFUNDABLE</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A49</informationType>
            </freeTextQualification>
            <freeText> - DATE OF ORIGIN</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILIES:    (ENTER FQFn FOR DETAILS, FXY FOR UPSELL)</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILY:FC1:1-2:LIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILY:FC2:3-4:LIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>NO BAG INCLUDED FOR AT LEAST ONE FLIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>TICKET STOCK RESTRICTION</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>BG CXR: 2*AF/2*AF</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>PRICED WITH VALIDATING CARRIER AF - REPRICE IF DIFFERENT VC</freeText>
        </textData>
        <offerReferences>
            <offerIdentifier>
            <uniqueOfferReference>SP2P-9175475112759695121-1-OI1-AI2</uniqueOfferReference>
            </offerIdentifier>
        </offerReferences>
        <surchargesGroup>
            <taxesAmount>
            <taxDetails>
                <rate>46.00</rate>
                <countryCode>YQ</countryCode>
                <type>AD</type>
            </taxDetails>
            <taxDetails>
                <rate>10.00</rate>
                <countryCode>YR</countryCode>
                <type>VA</type>
            </taxDetails>
            <taxDetails>
                <rate>240.00</rate>
                <countryCode>YR</countryCode>
                <type>VB</type>
            </taxDetails>
            <taxDetails>
                <rate>46.00</rate>
                <countryCode>CO</countryCode>
                <type>AE</type>
            </taxDetails>
            <taxDetails>
                <rate>28.00</rate>
                <countryCode>DG</countryCode>
                <type>VZ</type>
            </taxDetails>
            <taxDetails>
                <rate>57.30</rate>
                <countryCode>YS</countryCode>
                <type>TR</type>
            </taxDetails>
            <taxDetails>
                <rate>10.80</rate>
                <countryCode>FR</countryCode>
                <type>TI</type>
            </taxDetails>
            <taxDetails>
                <rate>33.90</rate>
                <countryCode>QX</countryCode>
                <type>AP</type>
            </taxDetails>
            <taxDetails>
                <rate>10.70</rate>
                <countryCode>DE</countryCode>
                <type>SE</type>
            </taxDetails>
            <taxDetails>
                <rate>62.10</rate>
                <countryCode>OY</countryCode>
                <type>CB</type>
            </taxDetails>
            <taxDetails>
                <rate>31.80</rate>
                <countryCode>RA</countryCode>
                <type>EB</type>
            </taxDetails>
            </taxesAmount>
        </surchargesGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>030424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>BOG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>CDG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>435</flightNumber>
                <bookingClass>R</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <flightTypeDetails>
                <flightIndicator>T</flightIndicator>
            </flightTypeDetails>
            <itemNumber>1</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>030424</departureDate>
                <arrivalDate>030424</arrivalDate>
            </productDateTimeDetails>
            <idNumber>CH25</idNumber>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>R</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>R</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>CH</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>040424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>CDG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>FRA</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>1018</flightNumber>
                <bookingClass>L</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <itemNumber>2</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>040424</departureDate>
                <arrivalDate>040424</arrivalDate>
            </productDateTimeDetails>
            <idNumber>CH25</idNumber>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>L</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>L</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>CH</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>180424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>FRA</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>CDG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>1619</flightNumber>
                <bookingClass>L</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <flightTypeDetails>
                <flightIndicator>T</flightIndicator>
            </flightTypeDetails>
            <itemNumber>3</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>180424</departureDate>
                <arrivalDate>180424</arrivalDate>
            </productDateTimeDetails>
            <idNumber>CH25</idNumber>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>L</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>L</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>CH</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>180424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>CDG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>BOG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>436</flightNumber>
                <bookingClass>R</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <itemNumber>4</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>180424</departureDate>
                <arrivalDate>180424</arrivalDate>
            </productDateTimeDetails>
            <idNumber>CH25</idNumber>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>R</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>R</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>CH</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <fareComponentDetailsGroup>
            <fareComponentID>
            <itemNumberDetails>
                <number>1</number>
                <type>FC</type>
            </itemNumberDetails>
            </fareComponentID>
            <marketFareComponent>
            <boardPointDetails>
                <trueLocationId>BOG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>FRA</trueLocationId>
            </offpointDetails>
            </marketFareComponent>
            <monetaryInformation>
            <monetaryDetails>
                <typeQualifier>TFC</typeQualifier>
                <amount>153.00</amount>
                <currency>NUC</currency>
            </monetaryDetails>
            </monetaryInformation>
            <componentClassInfo>
            <fareBasisDetails>
                <rateTariffClass>RL50TALB</rateTariffClass>
                <otherRateTariffClass>CH25</otherRateTariffClass>
            </fareBasisDetails>
            </componentClassInfo>
            <fareQualifiersDetail>
            <discountDetails>
                <fareQualifier>763</fareQualifier>
            </discountDetails>
            </fareQualifiersDetail>
            <fareFamilyDetails>
            <fareFamilyname>LIGHT</fareFamilyname>
            </fareFamilyDetails>
            <fareFamilyOwner>
            <companyIdentification>
                <otherCompany>AF</otherCompany>
            </companyIdentification>
            </fareFamilyOwner>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>1</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>2</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
        </fareComponentDetailsGroup>
        <fareComponentDetailsGroup>
            <fareComponentID>
            <itemNumberDetails>
                <number>2</number>
                <type>FC</type>
            </itemNumberDetails>
            </fareComponentID>
            <marketFareComponent>
            <boardPointDetails>
                <trueLocationId>FRA</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>BOG</trueLocationId>
            </offpointDetails>
            </marketFareComponent>
            <monetaryInformation>
            <monetaryDetails>
                <typeQualifier>TFC</typeQualifier>
                <amount>153.00</amount>
                <currency>NUC</currency>
            </monetaryDetails>
            </monetaryInformation>
            <componentClassInfo>
            <fareBasisDetails>
                <rateTariffClass>RL50TALB</rateTariffClass>
                <otherRateTariffClass>CH25</otherRateTariffClass>
            </fareBasisDetails>
            </componentClassInfo>
            <fareQualifiersDetail>
            <discountDetails>
                <fareQualifier>763</fareQualifier>
            </discountDetails>
            </fareQualifiersDetail>
            <fareFamilyDetails>
            <fareFamilyname>LIGHT</fareFamilyname>
            </fareFamilyDetails>
            <fareFamilyOwner>
            <companyIdentification>
                <otherCompany>AF</otherCompany>
            </companyIdentification>
            </fareFamilyOwner>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>3</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>4</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
        </fareComponentDetailsGroup>
        </fareInfoGroup>
    </pricingGroupLevelGroup>
    <pricingGroupLevelGroup>
        <numberOfPax>
        <segmentControlDetails>
            <quantity>3</quantity>
            <numberOfUnits>1</numberOfUnits>
        </segmentControlDetails>
        </numberOfPax>
        <passengersID>
        <travellerDetails>
            <measurementValue>1</measurementValue>
        </travellerDetails>
        </passengersID>
        <fareInfoGroup>
        <emptySegment>
            <fareDetails>
            <qualifier>766</qualifier>
            </fareDetails>
        </emptySegment>
        <pricingIndicators>
            <priceTariffType>I</priceTariffType>
            <productDateTimeDetails>
            <departureDate>030424</departureDate>
            </productDateTimeDetails>
            <companyDetails>
            <otherCompany>AF</otherCompany>
            </companyDetails>
        </pricingIndicators>
        <fareAmount>
            <monetaryDetails>
            <typeQualifier>B</typeQualifier>
            <amount>42.00</amount>
            <currency>USD</currency>
            </monetaryDetails>
            <otherMonetaryDetails>
            <typeQualifier>712</typeQualifier>
            <amount>92.00</amount>
            <currency>USD</currency>
            </otherMonetaryDetails>
        </fareAmount>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>4</textSubjectQualifier>
            <informationType>15</informationType>
            </freeTextQualification>
            <freeText>03APR24BOG AF X/PAR AF FRA20.40AF X/PAR AF BOG20.40 1S1.09NUC41.89END </freeText>
            <freeText>ROE1.00</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1P1</informationType>
            </freeTextQualification>
            <freeText>NON-REFUNDABLE</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A49</informationType>
            </freeTextQualification>
            <freeText> - DATE OF ORIGIN</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILIES:    (ENTER FQFn FOR DETAILS, FXY FOR UPSELL)</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILY:FC1:1-2:LIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>FARE FAMILY:FC2:3-4:LIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>NO BAG INCLUDED FOR AT LEAST ONE FLIGHT</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>TICKET STOCK RESTRICTION</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>BG CXR: 2*AF/2*AF</freeText>
        </textData>
        <textData>
            <freeTextQualification>
            <textSubjectQualifier>1</textSubjectQualifier>
            <informationType>1A0</informationType>
            </freeTextQualification>
            <freeText>PRICED WITH VALIDATING CARRIER AF - REPRICE IF DIFFERENT VC</freeText>
        </textData>
        <offerReferences>
            <offerIdentifier>
            <uniqueOfferReference>SP2P-9175475112759695121-1-OI1-AI3</uniqueOfferReference>
            </offerIdentifier>
        </offerReferences>
        <surchargesGroup>
            <taxesAmount>
            <taxDetails>
                <rate>46.00</rate>
                <countryCode>CO</countryCode>
                <type>AE</type>
            </taxDetails>
            <taxDetails>
                <rate>4.00</rate>
                <countryCode>YS</countryCode>
                <type>TR</type>
            </taxDetails>
            </taxesAmount>
        </surchargesGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>030424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>BOG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>CDG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>435</flightNumber>
                <bookingClass>R</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <flightTypeDetails>
                <flightIndicator>T</flightIndicator>
            </flightTypeDetails>
            <itemNumber>1</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>030424</departureDate>
                <arrivalDate>030424</arrivalDate>
            </productDateTimeDetails>
            <idNumber>IN90</idNumber>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>R</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>R</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>IN</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>040424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>CDG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>FRA</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>1018</flightNumber>
                <bookingClass>L</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <itemNumber>2</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>040424</departureDate>
                <arrivalDate>040424</arrivalDate>
            </productDateTimeDetails>
            <idNumber>IN90</idNumber>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>L</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>L</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>IN</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>180424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>FRA</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>CDG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>1619</flightNumber>
                <bookingClass>L</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <flightTypeDetails>
                <flightIndicator>T</flightIndicator>
            </flightTypeDetails>
            <itemNumber>3</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>180424</departureDate>
                <arrivalDate>180424</arrivalDate>
            </productDateTimeDetails>
            <idNumber>IN90</idNumber>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>L</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>L</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>IN</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <segmentLevelGroup>
            <segmentInformation>
            <flightDate>
                <departureDate>180424</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>CDG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>BOG</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>AF</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>436</flightNumber>
                <bookingClass>R</bookingClass>
                <operationalSuffix>X</operationalSuffix>
            </flightIdentification>
            <itemNumber>4</itemNumber>
            </segmentInformation>
            <additionalInformation>
            <productDateTimeDetails>
                <departureDate>180424</departureDate>
                <arrivalDate>180424</arrivalDate>
            </productDateTimeDetails>
            <idNumber>IN90</idNumber>
            </additionalInformation>
            <fareBasis>
            <additionalFareDetails>
                <rateClass>RL50TALB</rateClass>
                <secondRateClass>R</secondRateClass>
            </additionalFareDetails>
            </fareBasis>
            <cabinGroup>
            <cabinSegment>
                <bookingClassDetails>
                <designator>R</designator>
                <option>M</option>
                </bookingClassDetails>
            </cabinSegment>
            </cabinGroup>
            <baggageAllowance>
            <baggageDetails>
                <freeAllowance>0</freeAllowance>
                <quantityCode>N</quantityCode>
            </baggageDetails>
            </baggageAllowance>
            <ptcSegment>
            <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>IN</unitQualifier>
            </quantityDetails>
            </ptcSegment>
        </segmentLevelGroup>
        <fareComponentDetailsGroup>
            <fareComponentID>
            <itemNumberDetails>
                <number>1</number>
                <type>FC</type>
            </itemNumberDetails>
            </fareComponentID>
            <marketFareComponent>
            <boardPointDetails>
                <trueLocationId>BOG</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>FRA</trueLocationId>
            </offpointDetails>
            </marketFareComponent>
            <monetaryInformation>
            <monetaryDetails>
                <typeQualifier>TFC</typeQualifier>
                <amount>20.40</amount>
                <currency>NUC</currency>
            </monetaryDetails>
            </monetaryInformation>
            <componentClassInfo>
            <fareBasisDetails>
                <rateTariffClass>RL50TALB</rateTariffClass>
                <otherRateTariffClass>IN90</otherRateTariffClass>
            </fareBasisDetails>
            </componentClassInfo>
            <fareQualifiersDetail>
            <discountDetails>
                <fareQualifier>763</fareQualifier>
            </discountDetails>
            </fareQualifiersDetail>
            <fareFamilyDetails>
            <fareFamilyname>LIGHT</fareFamilyname>
            </fareFamilyDetails>
            <fareFamilyOwner>
            <companyIdentification>
                <otherCompany>AF</otherCompany>
            </companyIdentification>
            </fareFamilyOwner>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>1</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>2</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
        </fareComponentDetailsGroup>
        <fareComponentDetailsGroup>
            <fareComponentID>
            <itemNumberDetails>
                <number>2</number>
                <type>FC</type>
            </itemNumberDetails>
            </fareComponentID>
            <marketFareComponent>
            <boardPointDetails>
                <trueLocationId>FRA</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>BOG</trueLocationId>
            </offpointDetails>
            </marketFareComponent>
            <monetaryInformation>
            <monetaryDetails>
                <typeQualifier>TFC</typeQualifier>
                <amount>20.40</amount>
                <currency>NUC</currency>
            </monetaryDetails>
            </monetaryInformation>
            <componentClassInfo>
            <fareBasisDetails>
                <rateTariffClass>RL50TALB</rateTariffClass>
                <otherRateTariffClass>IN90</otherRateTariffClass>
            </fareBasisDetails>
            </componentClassInfo>
            <fareQualifiersDetail>
            <discountDetails>
                <fareQualifier>763</fareQualifier>
            </discountDetails>
            </fareQualifiersDetail>
            <fareFamilyDetails>
            <fareFamilyname>LIGHT</fareFamilyname>
            </fareFamilyDetails>
            <fareFamilyOwner>
            <companyIdentification>
                <otherCompany>AF</otherCompany>
            </companyIdentification>
            </fareFamilyOwner>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>3</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
            <couponDetailsGroup>
            <productId>
                <referenceDetails>
                <type>ST</type>
                <value>4</value>
                </referenceDetails>
            </productId>
            </couponDetailsGroup>
        </fareComponentDetailsGroup>
        </fareInfoGroup>
    </pricingGroupLevelGroup>
    </mainGroup>
</Fare_InformativePricingWithoutPNRReply>