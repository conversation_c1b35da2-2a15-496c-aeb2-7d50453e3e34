package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/yourorg/provider-service/core/amadeus"
)

// FlightInfoSOAPHandler handles POST /api/flightinfo requests using SOAP Amadeus
func FlightInfoSOAPHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("🚀🚀🚀 FLIGHTINFO HANDLER CALLED 🚀🚀🚀 - Method: %s, URL: %s", r.Method, r.URL.Path)
	log.Printf("🔧 DEBUG: FlightInfo handler started processing request")

	// Immediate test response to see if handler is called
	if r.URL.Query().Get("test") == "handler" {
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{"test": "handler_called", "status": "working"}`))
		return
	}

	if r.Method != http.MethodPost {
		log.Printf("❌ Method not allowed: %s", r.Method)
		http.Error(w, "method not allowed", http.StatusMethodNotAllowed)
		return
	}

	log.Printf("✅ Processing POST request to FlightInfo API")

	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	// Parse the simplified JSON request format
	var request struct {
		TUI           string `json:"TUI"`
		Provider      string `json:"Provider"`
		Currency      string `json:"Currency"`
		From          string `json:"From"`
		To            string `json:"To"`
		OnwardDate    string `json:"OnwardDate"`
		ReturnDate    string `json:"ReturnDate"`
		ADT           int    `json:"ADT"`
		CHD           int    `json:"CHD"`
		INF           int    `json:"INF"`
		Cabin         string `json:"Cabin"`
		FlightNo      string `json:"FlightNo"`
		Airline       string `json:"Airline"`
		BookingClass  string `json:"BookingClass"`
		FareBasisCode string `json:"FareBasisCode"`
	}

	if err := json.Unmarshal(body, &request); err != nil {
		log.Printf("JSON decode error: %v", err)
		http.Error(w, "invalid JSON payload: "+err.Error(), http.StatusBadRequest)
		return
	}

	// Validate required fields
	if request.From == "" || request.To == "" || request.OnwardDate == "" {
		log.Printf("Validation error: missing required fields")
		http.Error(w, "validation error: From, To, and OnwardDate are required", http.StatusBadRequest)
		return
	}

	// Convert to service request format
	serviceReq := amadeus.FlightInfoServiceRequest{
		TUI:           request.TUI,
		Provider:      request.Provider,
		Currency:      request.Currency,
		From:          request.From,
		To:            request.To,
		OnwardDate:    request.OnwardDate,
		ReturnDate:    request.ReturnDate,
		ADT:           request.ADT,
		CHD:           request.CHD,
		INF:           request.INF,
		Cabin:         request.Cabin,
		FlightNo:      request.FlightNo,
		Airline:       request.Airline,
		BookingClass:  request.BookingClass,
		FareBasisCode: request.FareBasisCode,
	}

	// Set default passenger counts if not provided
	if serviceReq.ADT == 0 && serviceReq.CHD == 0 && serviceReq.INF == 0 {
		serviceReq.ADT = 1 // Default to 1 adult
	}

	// Set default cabin if not provided
	if serviceReq.Cabin == "" {
		serviceReq.Cabin = "Y" // Default to Economy
	}

	log.Printf("Processing SOAP flight info request: From=%s, To=%s, Date=%s, ADT=%d, CHD=%d, INF=%d",
		serviceReq.From, serviceReq.To, serviceReq.OnwardDate, serviceReq.ADT, serviceReq.CHD, serviceReq.INF)

	// Read Amadeus config from env
	baseEndpoint := os.Getenv("AMADEUS_ENDPOINT")
	wsap := os.Getenv("AMADEUS_WSAP")
	username := os.Getenv("AMADEUS_USERNAME")
	password := os.Getenv("AMADEUS_PASSWORD")
	office := os.Getenv("AMADEUS_OFFICE")

	log.Printf("🔧 DEBUG: AMADEUS_ENDPOINT = %s", baseEndpoint)
	log.Printf("🔧 DEBUG: AMADEUS_WSAP = %s", wsap)

	if baseEndpoint == "" {
		log.Printf("Missing AMADEUS_ENDPOINT environment variable")
		http.Error(w, "missing AMADEUS_ENDPOINT env var", http.StatusInternalServerError)
		return
	}

	if username == "" || password == "" || office == "" {
		log.Printf("Missing required Amadeus credentials")
		http.Error(w, "missing Amadeus credentials", http.StatusInternalServerError)
		return
	}

	// Build full endpoint with WSAP
	var fullEndpoint string
	if wsap != "" {
		fullEndpoint = strings.TrimRight(baseEndpoint, "/") + "/" + wsap
	} else {
		fullEndpoint = baseEndpoint
	}

	log.Printf("Using Amadeus endpoint: %s", fullEndpoint)

	// Create SOAP client - NOW FIXED with correct URL and password digest
	soapClient := amadeus.NewSOAPClient(fullEndpoint, username, password, office)

	log.Printf("🔧 DEBUG: Using FIXED SOAP client for TIPNRQ API: %s -> %s on %s",
		serviceReq.From, serviceReq.To, serviceReq.OnwardDate)

	// Process the flight info request with FIXED SOAP client
	log.Printf("🔧 DEBUG: About to call ProcessFlightInfoRequest with serviceReq: %+v", serviceReq)
	jsonResponse, err := amadeus.ProcessFlightInfoRequest(soapClient, serviceReq)
	if err != nil {
		log.Printf("❌ Amadeus SOAP Flight Info API error: %v", err)

		// Return error response in expected format
		errorResponse := map[string]interface{}{
			"TUI":   serviceReq.TUI,
			"Code":  "500",
			"Msg":   []string{fmt.Sprintf("Amadeus Flight Info API Error: %v", err)},
			"Trips": []interface{}{},
		}

		errorJSON, _ := json.Marshal(errorResponse)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadGateway)
		w.Write(errorJSON)
		return
	}

	log.Printf("✅ ProcessFlightInfoRequest completed successfully, response length: %d bytes", len(jsonResponse))

	log.Printf("Successfully processed SOAP flight info request, returning JSON response")
	w.Header().Set("Content-Type", "application/json")
	w.Write(jsonResponse)
}
