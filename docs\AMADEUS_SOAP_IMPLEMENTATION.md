# Amadeus SOAP API Implementation Guide

## Overview

This document provides a comprehensive guide to the Amadeus SOAP API implementation based on your SAMPLE folder and response format requirements. The implementation includes 3 main APIs: FlightInfo, FareRule, and SSR.

## 🎯 API Services Mapping

| API | Amadeus Service | SOAP Action | Purpose |
|-----|----------------|-------------|---------|
| **FlightInfo** | `Fare_InformativePricingWithoutPNR` | `TIPNRQ_18_1_1A` | Get detailed flight pricing without PNR |
| **FareRule** | `Fare_CheckRules` | `FARQNR_07_1_1A` | Get fare rules and restrictions |
| **SSR** | `PNR_AddMultiElements` | `PNRADD_22_1_1A` | Add SSR elements to PNR |

## 🔧 Implementation Architecture

### Core Components

1. **`core/amadeus/soap_client.go`** - Main SOAP client with WS-Security
2. **`core/amadeus/flightinfo_service.go`** - TIPNRQ service implementation
3. **`core/amadeus/farerule_service.go`** - FARQNR service implementation
4. **`core/amadeus/ssr_service.go`** - PNRADD service implementation
5. **API Handlers** - HTTP request/response handling

### SOAP Client Features

- ✅ **WS-Security Authentication** - Username token with password digest
- ✅ **Hosted User Authentication** - AMA_SecurityHostedUser header
- ✅ **Message ID Generation** - Unique UUID for each request
- ✅ **XML Request/Response Logging** - For debugging
- ✅ **Error Handling** - SOAP fault extraction
- ✅ **Timeout Management** - Configurable request timeout

## 📋 API Request/Response Details

### 1. FlightInfo API (`/api/flightinfo`)

**Amadeus Service:** `Fare_InformativePricingWithoutPNR` (TIPNRQ_18_1_1A)

#### Request Format:
```json
{
  "TUI": "ON24038a4b-01e0-4c6c-bc01-bd27be37a611|5a18f415-7c86-42be-91ae-2496a424e7c6|20250819145137",
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-19",
  "ReturnDate": "",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "Y"
}
```

#### Amadeus SOAP Request Structure:
```xml
<Fare_InformativePricingWithoutPNR>
  <passengersGroup>
    <segmentRepetitionControl>
      <segmentControlDetails>
        <quantity>1</quantity>
        <numberOfUnits>1</numberOfUnits>
      </segmentControlDetails>
    </segmentRepetitionControl>
    <paxReference>
      <ptc>ADT</ptc>
      <traveller><ref>1</ref></traveller>
    </paxReference>
  </passengersGroup>
  <segmentGroup>
    <segmentInformation>
      <flightDate>
        <departureDate>190825</departureDate>
      </flightDate>
      <boardPointDetails>
        <trueLocationId>BOM</trueLocationId>
      </boardPointDetails>
      <offpointDetails>
        <trueLocationId>DEL</trueLocationId>
      </offpointDetails>
    </segmentInformation>
  </segmentGroup>
  <pricingOptionGroup>
    <pricingOptionKey>
      <pricingOptionKey>RP</pricingOptionKey>
    </pricingOptionKey>
  </pricingOptionGroup>
  <conversionRate>
    <conversionRateDetail>
      <currency>INR</currency>
    </conversionRateDetail>
  </conversionRate>
</Fare_InformativePricingWithoutPNR>
```

#### Response Format:
```json
{
  "TUI": "ON24038a4b-01e0-4c6c-bc01-bd27be37a611|5a18f415-7c86-42be-91ae-2496a424e7c6|20250819145137",
  "ADT": 1,
  "CHD": 0,
  "CeilingInfo": "R0",
  "Code": "200",
  "FareRecommendations": [{
    "JourneyKey": "BOM-DEL,2007,6E,2025-08-19,E",
    "Recommendations": [{
      "Index": "6E|20",
      "FareKey": "INR,R,5448",
      "NetFare": 5448,
      "RBD": "R",
      "FareClass": "J",
      "FBC": "RUIP"
    }]
  }],
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-19",
  "GrossAmount": 5295,
  "NetAmount": 5240,
  "Trips": [...]
}
```

### 2. FareRule API (`/api/farerule`)

**Amadeus Service:** `Fare_CheckRules` (FARQNR_07_1_1A)

#### Request Format:
```json
{
  "TUI": "ON24038a4b-01e0-4c6c-bc01-bd27be37a611|29a29964-edab-4d33-a71d-fc1da5552c14|20250819145114",
  "From": "BOM",
  "To": "DEL",
  "FlightNo": "2007",
  "Provider": "6E",
  "FUID": "1",
  "FareKey": "INR,R,5448",
  "FareComponent": 1,
  "RuleCategories": ["PE", "MX", "SR", "VC", "VR"]
}
```

#### Amadeus SOAP Request Structure:
```xml
<Fare_CheckRules xmlns="http://xml.amadeus.com/FARQNR_07_1_1A">
  <msgType>
    <messageFunctionDetails>
      <messageFunction>712</messageFunction>
    </messageFunctionDetails>
  </msgType>
  <itemNumber>
    <itemNumberDetails>
      <number>1</number>
    </itemNumberDetails>
    <itemNumberDetails>
      <number>1</number>
      <type>FC</type>
    </itemNumberDetails>
  </itemNumber>
  <fareRule>
    <tarifFareRule>
      <ruleSectionId>PE</ruleSectionId>
      <ruleSectionId>MX</ruleSectionId>
      <ruleSectionId>SR</ruleSectionId>
      <ruleSectionId>VC</ruleSectionId>
      <ruleSectionId>VR</ruleSectionId>
    </tarifFareRule>
  </fareRule>
</Fare_CheckRules>
```

#### Response Format:
```json
{
  "TUI": "ON24038a4b-01e0-4c6c-bc01-bd27be37a611|29a29964-edab-4d33-a71d-fc1da5552c14|20250819145114",
  "Code": "200",
  "Msg": ["Success"],
  "Trips": [{
    "Journey": [{
      "Provider": "6E",
      "SpecialInformations": "",
      "Segments": [{
        "FUID": "1",
        "VAC": "6E",
        "Rules": [{
          "Rule": [{
            "Head": "Change Fee",
            "Info": [{
              "AdultAmount": "Not-Permitted",
              "ChildAmount": "",
              "InfantAmount": "",
              "CurrencyCode": "",
              "Description": "0 HRS - 24 HRS To Departure",
              "RuleText": ""
            }]
          }]
        }]
      }]
    }]
  }]
}
```

### 3. SSR API (`/api/ssr`)

**Amadeus Service:** `PNR_AddMultiElements` (PNRADD_22_1_1A)

#### Request Format:
```json
{
  "TUI": "ON24038a4b-01e0-4c6c-bc01-bd27be37a611|29a29964-edab-4d33-a71d-fc1da5552c14|20250819145114",
  "From": "BOM",
  "To": "DEL",
  "FlightNo": "2007",
  "Provider": "6E",
  "FUID": "0",
  "PTC": "ADT"
}
```

#### Amadeus SOAP Request Structure:
```xml
<PNR_AddMultiElements xmlns="http://xml.amadeus.com/PNRADD_22_1_1A">
  <pnrActions>
    <optionCode>0</optionCode>
  </pnrActions>
  <travellerInfo>
    <elementManagementPassenger>
      <reference>
        <qualifier>PR</qualifier>
        <number>1</number>
      </reference>
      <segmentName>NM</segmentName>
    </elementManagementPassenger>
    <passengerData>
      <travellerInformation>
        <traveller>
          <surname>DOE</surname>
          <quantity>1</quantity>
        </traveller>
        <passenger>
          <firstName>JOHN</firstName>
        </passenger>
      </travellerInformation>
    </passengerData>
  </travellerInfo>
  <dataElementsMaster>
    <marker1/>
    <dataElementsIndiv>
      <elementManagementData>
        <segmentName>SSR</segmentName>
      </elementManagementData>
      <serviceRequest>
        <ssr>
          <type>WCHR</type>
          <freeText>WHEELCHAIR REQUIRED</freeText>
        </ssr>
      </serviceRequest>
    </dataElementsIndiv>
  </dataElementsMaster>
</PNR_AddMultiElements>
```

#### Response Format:
```json
{
  "TUI": "ON24038a4b-01e0-4c6c-bc01-bd27be37a611|29a29964-edab-4d33-a71d-fc1da5552c14|20250819145114",
  "Code": "200",
  "Msg": ["Success"],
  "PaidSSR": false,
  "Trips": [{
    "From": "BOM",
    "To": "DEL",
    "Journey": [{
      "Provider": "6E",
      "MultiSSR": "",
      "ConversationID": "ABC123",
      "Segments": [{
        "FUID": "0",
        "VAC": "6E",
        "Index": null,
        "SSR": [{
          "Code": "BAG",
          "Description": "15Kg, 7Kg",
          "Charge": 0,
          "OrginalCharge": 0,
          "VAT": 0,
          "Category": "",
          "ID": 1,
          "PTC": "ADT",
          "Type": "2"
        }]
      }]
    }]
  }]
}
```

## 🔐 Authentication & Security

### WS-Security Implementation

The SOAP client implements WS-Security with:

1. **Username Token Authentication**
2. **Password Digest** (SHA-1 hash of nonce + created + password)
3. **Nonce** (16-byte random value, base64 encoded)
4. **Created Timestamp** (UTC timestamp)

### Hosted User Authentication

Amadeus requires additional hosted user authentication:

```xml
<sec:AMA_SecurityHostedUser xmlns:sec="http://xml.amadeus.com/2010/06/Security_v1">
  <sec:UserID AgentDutyCode="SU" POS_Type="1" RequestorType="U" PseudoCityCode="OFFICE_CODE"/>
</sec:AMA_SecurityHostedUser>
```

## ⚙️ Environment Configuration

```env
# Amadeus SOAP Configuration
AMADEUS_ENDPOINT=https://noded5.test.webservices.amadeus.com
AMADEUS_USERNAME=WSB2TTBS
AMADEUS_PASSWORD=AMADEUS100
AMADEUS_OFFICE=DELVS38BX

# Service Port
PORT=8001
```

## 🚀 Usage Examples

### Start the Service
```bash
go run cmd/provider/main.go
# Server starts on http://localhost:8001
```

### Test the APIs
```bash
# Health check
curl http://localhost:8001/

# Test FlightInfo API
curl -X POST http://localhost:8001/api/flightinfo \
  -H "Content-Type: application/json" \
  -d @test/sample-requests/flightinfo.json

# Test FareRule API  
curl -X POST http://localhost:8001/api/farerule \
  -H "Content-Type: application/json" \
  -d @test/sample-requests/farerule.json

# Test SSR API
curl -X POST http://localhost:8001/api/ssr \
  -H "Content-Type: application/json" \
  -d @test/sample-requests/ssr.json
```

## 🔍 Debugging

### XML Request/Response Logging

The implementation automatically saves XML requests and responses for debugging:

- `last_fare_informativepricingwithoutpnr_request.xml`
- `last_fare_informativepricingwithoutpnr_response.xml`
- `last_fare_checkrules_request.xml`
- `last_fare_checkrules_response.xml`
- `last_pnr_addmultielements_request.xml`
- `last_pnr_addmultielements_response.xml`

### Error Handling

The implementation includes comprehensive error handling:

1. **SOAP Fault Detection** - Extracts and reports SOAP faults
2. **HTTP Error Handling** - Proper HTTP status codes
3. **JSON Error Responses** - Consistent error format
4. **Logging** - Detailed request/response logging

## 📊 Key Features

### FlightInfo Service (TIPNRQ)
- ✅ **No PNR Required** - Get pricing without booking
- ✅ **Multi-Passenger Support** - ADT/CHD/INF handling
- ✅ **Currency Conversion** - INR currency support
- ✅ **Pricing Options** - RP, RU, TAC pricing types
- ✅ **Round Trip Support** - Return date handling

### FareRule Service (FARQNR)
- ✅ **Message Function 712** - Proper fare rule request
- ✅ **Multiple Rule Categories** - PE, MX, SR, VC, VR
- ✅ **Fare Component Support** - FC type handling
- ✅ **Structured Rule Parsing** - Convert free text to structured data

### SSR Service (PNRADD)
- ✅ **Multiple SSR Types** - WCHR, VGML, SEAT, etc.
- ✅ **Contact Information** - Email and mobile support
- ✅ **PNR Creation** - Full PNR with SSR elements
- ✅ **Passenger Information** - Name and contact details

## 🎯 Next Steps

1. **Test with Real Credentials** - Update `.env` with production Amadeus credentials
2. **Enhance XML Parsing** - Implement detailed XML response parsing
3. **Add Business Logic** - Implement fare calculation and validation
4. **Error Recovery** - Add retry logic and fallback mechanisms
5. **Performance Optimization** - Add caching and connection pooling

This implementation provides a solid foundation for Amadeus SOAP integration with proper authentication, error handling, and response formatting matching your exact requirements.
