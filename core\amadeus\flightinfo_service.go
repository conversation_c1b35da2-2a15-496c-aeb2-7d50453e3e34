package amadeus

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/clbanning/mxj"
)

// FlightInfoServiceRequest represents the request for flight information using TIPNRQ
type FlightInfoServiceRequest struct {
	TUI           string
	Provider      string
	Currency      string
	From          string
	To            string
	OnwardDate    string
	ReturnDate    string
	ADT           int
	CHD           int
	INF           int
	Cabin         string
	FlightNo      string
	Airline       string
	BookingClass  string
	FareBasisCode string
}

// Enhanced request structures for comprehensive Amadeus integration
type EnhancedFlightInfoRequest struct {
	TUI       string     `json:"TUI"`
	Provider  string     `json:"Provider"`
	Currency  string     `json:"Currency"`
	Travelers []Traveler `json:"Travelers"`
	Itinerary []Segment  `json:"Itinerary"`
}

type Traveler struct {
	Type  string `json:"Type"` // ADT, CHD, INF
	Count int    `json:"Count"`
}

type Segment struct {
	Origin        string `json:"Origin"`
	Destination   string `json:"Destination"`
	DepartureDate string `json:"DepartureDate"`
	FlightNo      string `json:"FlightNo"`
	Airline       string `json:"Airline"`
	BookingClass  string `json:"BookingClass"`
	Cabin         string `json:"Cabin"`
	FareBasisCode string `json:"FareBasisCode"`
}

// ConvertEnhancedToStandardRequest converts enhanced request format to standard format
func ConvertEnhancedToStandardRequest(enhanced EnhancedFlightInfoRequest) FlightInfoServiceRequest {
	// Extract passenger counts
	var adt, chd, inf int
	for _, traveler := range enhanced.Travelers {
		switch traveler.Type {
		case "ADT":
			adt = traveler.Count
		case "CHD":
			chd = traveler.Count
		case "INF":
			inf = traveler.Count
		}
	}

	// Extract first segment information
	var from, to, onwardDate, flightNo, provider, cabin, bookingClass string
	if len(enhanced.Itinerary) > 0 {
		segment := enhanced.Itinerary[0]
		from = segment.Origin
		to = segment.Destination
		onwardDate = segment.DepartureDate
		flightNo = segment.FlightNo
		provider = segment.Airline
		cabin = segment.Cabin
		bookingClass = segment.BookingClass
		// fareBasisCode = segment.FareBasisCode // Will be used in future enhancements
	}

	// Set currency
	currency := enhanced.Currency
	if currency == "" {
		currency = "INR"
	}

	// Use BookingClass for Amadeus XML, fallback to Cabin if not provided
	amadeusBookingClass := bookingClass
	if amadeusBookingClass == "" {
		// Map cabin to default booking class
		switch cabin {
		case "E", "Y":
			amadeusBookingClass = "Y"
		case "C":
			amadeusBookingClass = "C"
		case "F":
			amadeusBookingClass = "F"
		default:
			amadeusBookingClass = "Y"
		}
	}

	return FlightInfoServiceRequest{
		TUI:        enhanced.TUI,
		From:       from,
		To:         to,
		OnwardDate: onwardDate,
		ReturnDate: "", // Extract from second segment if exists
		ADT:        adt,
		CHD:        chd,
		INF:        inf,
		Cabin:      amadeusBookingClass, // Use booking class for Amadeus
		Currency:   currency,
		Provider:   provider,
		FlightNo:   flightNo,
	}
}

// BuildFareInformativePricingRequestXML builds the SOAP body for Fare_InformativePricingWithoutPNR
func BuildFareInformativePricingRequestXML(req FlightInfoServiceRequest) string {
	log.Printf("🔧 DEBUG: BuildFareInformativePricingRequestXML called for %s->%s", req.From, req.To)

	// Set defaults
	if req.ADT == 0 && req.CHD == 0 && req.INF == 0 {
		req.ADT = 1
	}
	if req.Cabin == "" {
		req.Cabin = "Y"
	}
	if req.Currency == "" {
		req.Currency = "INR"
	}
	if req.BookingClass == "" {
		req.BookingClass = getCabinClass(req.Cabin)
	}

	// Convert date from YYYY-MM-DD to DDMMYY format
	formattedDate := formatDateForAmadeus(req.OnwardDate)

	// Build passenger groups first
	passengerGroups := buildPassengerGroups(req.ADT, req.CHD, req.INF)
	log.Printf("🔧 DEBUG: Generated passenger groups: %s", passengerGroups)

	// Build segment groups for multi-segment flights
	segmentGroups := buildSegmentGroupsForMultiSegment(req, formattedDate)

	// Build the complete Fare_InformativePricingWithoutPNR request body
	bodyXML := fmt.Sprintf(`
<Fare_InformativePricingWithoutPNR xmlns="http://xml.amadeus.com/TIPNRQ_18_1_1A">
%s
%s
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>FCO</pricingOptionKey>
		</pricingOptionKey>
		<currency>
			<firstCurrencyDetails>
				<currencyQualifier>FCO</currencyQualifier>
				<currencyIsoCode>%s</currencyIsoCode>
			</firstCurrencyDetails>
		</currency>
	</pricingOptionGroup>
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>RLO</pricingOptionKey>
		</pricingOptionKey>
	</pricingOptionGroup>
</Fare_InformativePricingWithoutPNR>`,
		passengerGroups,
		segmentGroups,
		req.Currency)

	log.Printf("🔧 DEBUG: Generated Fare_InformativePricingWithoutPNR XML (length: %d)", len(bodyXML))
	return bodyXML
}

// buildPassengerGroups creates passenger groups for Fare_InformativePricingWithoutPNR
// Following the exact structure from Amadeus documentation
func buildPassengerGroups(adt, chd, inf int) string {
	var groups []string
	passengerID := 1

	// Adult passengers - quantity=1 for ADT group
	if adt > 0 {
		adultGroup := fmt.Sprintf(`
	<passengersGroup>
		<segmentRepetitionControl>
			<segmentControlDetails>
				<quantity>1</quantity>
				<numberOfUnits>%d</numberOfUnits>
			</segmentControlDetails>
		</segmentRepetitionControl>
		<travellersID>`, adt)

		// Add all adult traveller IDs in one travellersID block
		for i := 0; i < adt; i++ {
			adultGroup += fmt.Sprintf(`
			<travellerDetails>
				<measurementValue>%d</measurementValue>
			</travellerDetails>`, passengerID)
			passengerID++
		}

		adultGroup += `
		</travellersID>
		<discountPtc>
			<valueQualifier>ADT</valueQualifier>
		</discountPtc>
	</passengersGroup>`
		groups = append(groups, adultGroup)
	}

	// Child passengers - quantity=2 for CHD group
	if chd > 0 {
		childGroup := fmt.Sprintf(`
	<passengersGroup>
		<segmentRepetitionControl>
			<segmentControlDetails>
				<quantity>2</quantity>
				<numberOfUnits>%d</numberOfUnits>
			</segmentControlDetails>
		</segmentRepetitionControl>
		<travellersID>`, chd)

		// Add all child traveller IDs in one travellersID block
		for i := 0; i < chd; i++ {
			childGroup += fmt.Sprintf(`
			<travellerDetails>
				<measurementValue>%d</measurementValue>
			</travellerDetails>`, passengerID)
			passengerID++
		}

		childGroup += `
		</travellersID>
		<discountPtc>
			<valueQualifier>CNN</valueQualifier>
		</discountPtc>
	</passengersGroup>`
		groups = append(groups, childGroup)
	}

	// Infant passengers - quantity=3 for INF group
	if inf > 0 {
		infantGroup := fmt.Sprintf(`
	<passengersGroup>
		<segmentRepetitionControl>
			<segmentControlDetails>
				<quantity>3</quantity>
				<numberOfUnits>%d</numberOfUnits>
			</segmentControlDetails>
		</segmentRepetitionControl>
		<travellersID>`, inf)

		// Infants are typically associated with first adult (ref=1)
		for i := 0; i < inf; i++ {
			infantGroup += `
			<travellerDetails>
				<measurementValue>1</measurementValue>
			</travellerDetails>`
		}

		infantGroup += `
		</travellersID>
		<discountPtc>
			<valueQualifier>INF</valueQualifier>
			<fareDetails>
				<qualifier>766</qualifier>
			</fareDetails>
		</discountPtc>
	</passengersGroup>`
		groups = append(groups, infantGroup)
	}

	return strings.Join(groups, "")
}

// buildSegmentGroups creates segment groups for Fare_InformativePricingWithoutPNR
func buildSegmentGroups(req FlightInfoServiceRequest, formattedDate string) string {
	// For FlightInfo API, we typically have one segment
	// This follows the Amadeus documentation format exactly
	segmentXML := fmt.Sprintf(`
	<segmentGroup>
		<segmentInformation>
			<flightDate>
				<departureDate>%s</departureDate>
			</flightDate>
			<boardPointDetails>
				<trueLocationId>%s</trueLocationId>
			</boardPointDetails>
			<offpointDetails>
				<trueLocationId>%s</trueLocationId>
			</offpointDetails>
			<companyDetails>
				<marketingCompany>%s</marketingCompany>
			</companyDetails>
			<flightIdentification>
				<flightNumber>%s</flightNumber>
				<bookingClass>%s</bookingClass>
			</flightIdentification>
			<flightTypeDetails>
				<flightIndicator>1</flightIndicator>
			</flightTypeDetails>
			<itemNumber>1</itemNumber>
		</segmentInformation>
	</segmentGroup>`,
		formattedDate,
		req.From,
		req.To,
		getAirlineCode(req.Airline),
		getFlightNumber(req),
		req.BookingClass)

	return segmentXML
}

// buildPricingOptionGroups creates pricing option groups for Fare_InformativePricingWithoutPNR
func buildPricingOptionGroups(airline, currency string) string {
	var options []string

	// Validating Carrier option (VC)
	if airline != "" {
		vcOption := fmt.Sprintf(`
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>VC</pricingOptionKey>
		</pricingOptionKey>
		<carrierInformation>
			<companyIdentification>
				<otherCompany>%s</otherCompany>
			</companyIdentification>
		</carrierInformation>
	</pricingOptionGroup>`, getAirlineCode(airline))
		options = append(options, vcOption)
	}

	// Reprice option (RP)
	rpOption := `
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>RP</pricingOptionKey>
		</pricingOptionKey>
	</pricingOptionGroup>`
	options = append(options, rpOption)

	// Reprice with Uniform option (RU)
	ruOption := `
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>RU</pricingOptionKey>
		</pricingOptionKey>
	</pricingOptionGroup>`
	options = append(options, ruOption)

	// Reprice with Lowest option (RLO) - recommended to avoid duplicate fares
	rloOption := `
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>RLO</pricingOptionKey>
		</pricingOptionKey>
	</pricingOptionGroup>`
	options = append(options, rloOption)

	// Currency option (FCO)
	if currency != "" {
		fcoOption := fmt.Sprintf(`
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>FCO</pricingOptionKey>
		</pricingOptionKey>
		<currency>
			<firstCurrencyDetails>
				<currencyQualifier>FCO</currencyQualifier>
				<currencyIsoCode>%s</currencyIsoCode>
			</firstCurrencyDetails>
		</currency>
	</pricingOptionGroup>`, currency)
		options = append(options, fcoOption)
	}

	return strings.Join(options, "")
}

// Helper functions
func getProviderCode(req FlightInfoServiceRequest) string {
	if req.Provider != "" {
		return req.Provider
	}
	return "AI" // Default to Air India
}

func getFlightNumber(req FlightInfoServiceRequest) string {
	if req.FlightNo != "" {
		return req.FlightNo
	}
	return "2986" // Default flight number
}

func getAirlineCode(airline string) string {
	if airline != "" {
		return airline
	}
	return "AI" // Default to Air India
}

// getCabinClass maps cabin codes to Amadeus service class codes
func getCabinClass(cabin string) string {
	switch cabin {
	case "E", "Y":
		return "Y" // Economy
	case "C":
		return "C" // Business
	case "F":
		return "F" // First
	default:
		return "Y" // Default to Economy
	}
}

// ProcessFlightInfoRequest processes flight info request using Fare_InformativePricingWithoutPNR
func ProcessFlightInfoRequest(client *SOAPClient, req FlightInfoServiceRequest) ([]byte, error) {
	log.Printf("Processing flight info request: %s -> %s on %s", req.From, req.To, req.OnwardDate)

	// Build Fare_InformativePricingWithoutPNR request XML
	bodyXML := BuildFareInformativePricingRequestXML(req)

	// Send SOAP request using the correct service key (TIPNRQ instead of full name)
	responseXML, err := client.SendSOAPRequest("TIPNRQ", bodyXML)
	if err != nil {
		return nil, fmt.Errorf("SOAP request failed: %v", err)
	}

	// Check for SOAP fault and return proper JSON error response
	if fault, err := client.ExtractSOAPFault(responseXML); err == nil && fault != "" {
		log.Printf("Amadeus SOAP fault received: %s", fault)

		// Create JSON error response for SOAP faults
		errorResponse := map[string]interface{}{
			"TUI":                 req.TUI,
			"Code":                "500",
			"Msg":                 []string{fmt.Sprintf("Amadeus Flight Info API Error: SOAP fault: %s", fault)},
			"ADT":                 req.ADT,
			"CHD":                 req.CHD,
			"INF":                 req.INF,
			"From":                req.From,
			"To":                  req.To,
			"OnwardDate":          req.OnwardDate,
			"ReturnDate":          req.ReturnDate,
			"CeilingInfo":         "",
			"FareRecommendations": []interface{}{},
		}

		jsonResponse, _ := json.Marshal(errorResponse)
		return jsonResponse, nil // Return as successful JSON response with error code
	}

	// Transform XML response to JSON
	jsonResponse, err := TransformFareInformativePricingToFlightInfo(responseXML, req)
	if err != nil {
		return nil, fmt.Errorf("failed to transform response: %v", err)
	}

	return jsonResponse, nil
}

// TransformFareInformativePricingToFlightInfo transforms Fare_InformativePricingWithoutPNR XML response to FlightInfo JSON format
func TransformFareInformativePricingToFlightInfo(xmlBytes []byte, req FlightInfoServiceRequest) ([]byte, error) {
	// Parse XML response
	parsedXML, err := mxj.NewMapXml(xmlBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse XML: %v", err)
	}

	// Generate TUI if not provided
	tui := req.TUI
	if tui == "" {
		tui = fmt.Sprintf("ON24%s|%s|%s",
			generateRandomString(8),
			generateRandomString(36),
			time.Now().Format("20060102150405"))
	}

	// Extract fare information from Fare_InformativePricingWithoutPNR response
	fareInfo := extractFareInfoFromInformativePricing(parsedXML)

	// Create response structure matching your expected format with detailed flight information
	response := map[string]interface{}{
		"TUI":                 tui,
		"Code":                "200",
		"Msg":                 []string{"Success"},
		"ADT":                 req.ADT,
		"CHD":                 req.CHD,
		"INF":                 req.INF,
		"CeilingInfo":         "R0",
		"FareRecommendations": generateFlightInfoFareRecommendations(req, fareInfo),
		"From":                req.From,
		"To":                  req.To,
		"OnwardDate":          req.OnwardDate,
		"ReturnDate":          req.ReturnDate,
		"GrossAmount":         fareInfo.GrossAmount,
		"NetAmount":           fareInfo.NetAmount,
		"SSRAmount":           0,
		"GeneralKeys":         nil,
		"Trips":               generateFlightInfoTripsFromInformativePricing(req, fareInfo),
	}

	// Convert to JSON
	jsonBytes, err := json.MarshalIndent(response, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %v", err)
	}

	return jsonBytes, nil
}

// TaxDetail holds individual tax information from Amadeus
type TaxDetail struct {
	Code   string // Tax code (YR, K3, P2, etc.)
	Type   string // Tax type (VA, VB, DO, CB, AF, etc.)
	Amount int    // Tax amount
}

// FareInfo holds extracted fare information from Fare_InformativePricingWithoutPNR response
type FareInfo struct {
	GrossAmount      int
	NetAmount        int
	BaseFare         int
	Taxes            int
	TaxDetails       []TaxDetail // Individual tax breakdown from Amadeus
	FareClass        string
	RBD              string
	FBC              string
	Provider         string
	FlightNumber     string
	DepartureTime    string
	ArrivalTime      string
	Duration         string
	Aircraft         string
	BaggageAllowance string   // Baggage allowance (e.g., "15kg")
	BaggageCode      string   // Baggage quantity code (e.g., "700")
	FareRules        []string // Fare rules and restrictions
	DepartureDate    string   // Departure date from XML
	ArrivalDate      string   // Arrival date from XML
	// Real Amadeus data (no hardcoded values)
	AirlineName       string // Real airline name from Amadeus
	AircraftType      string // Real aircraft type from Amadeus
	OriginName        string // Real origin airport name from Amadeus
	DestinationName   string // Real destination airport name from Amadeus
	DepartureTerminal string // Real departure terminal from Amadeus
	ArrivalTerminal   string // Real arrival terminal from Amadeus
	SeatAvailability  int    // Real seat count from Amadeus
	RefundableStatus  string // Real refundability from Amadeus
	EquipmentCode     string // Real equipment code from Amadeus
}

// extractFareInfoFromInformativePricing extracts fare information from Fare_InformativePricingWithoutPNR XML response
func extractFareInfoFromInformativePricing(parsedXML map[string]interface{}) FareInfo {
	// Initialize with default values
	fareInfo := FareInfo{
		GrossAmount:   0,
		NetAmount:     0,
		BaseFare:      0,
		Taxes:         0,
		FareClass:     "ECONOMY",
		RBD:           "M",
		FBC:           "",
		Provider:      "AI",
		FlightNumber:  "",
		DepartureTime: "",
		ArrivalTime:   "",
		Duration:      "",
		Aircraft:      "",
	}

	// Navigate through the XML structure to extract actual values from Fare_InformativePricingWithoutPNR response
	log.Printf("🔍 DEBUG: Parsing Amadeus XML response for real fare data...")
	log.Printf("🔍 DEBUG: Parsed XML root keys: %v", getMapKeys(parsedXML))

	// Check for Amadeus errors first
	if envelope, exists := parsedXML["Envelope"]; exists {
		if envelopeMap, ok := envelope.(map[string]interface{}); ok {
			if body, exists := envelopeMap["Body"]; exists {
				if bodyMap, ok := body.(map[string]interface{}); ok {
					if reply, exists := bodyMap["Fare_InformativePricingWithoutPNRReply"]; exists {
						if replyMap, ok := reply.(map[string]interface{}); ok {
							// Check response type - 8 indicates error
							if messageDetails, exists := replyMap["messageDetails"]; exists {
								if msgMap, ok := messageDetails.(map[string]interface{}); ok {
									if responseType, exists := msgMap["responseType"]; exists {
										if responseTypeStr, ok := responseType.(string); ok && responseTypeStr == "8" {
											log.Printf("❌ AMADEUS ERROR: Response type 8 detected - returning default values")
											// Extract error details
											if errorGroup, exists := replyMap["errorGroup"]; exists {
												extractAmadeusError(errorGroup)
											}
											return fareInfo // Return default values for error case
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// The mxj library strips namespaces, so we use "Envelope" instead of "soapenv:Envelope"
	var envelope interface{}
	var exists bool
	if envelope, exists = parsedXML["Envelope"]; exists {
		log.Printf("🔍 DEBUG: Found Envelope (namespace stripped)")
	} else {
		log.Printf("❌ ERROR: No Envelope found in parsed XML")
		return fareInfo
	}

	if envelopeMap, ok := envelope.(map[string]interface{}); ok {
		log.Printf("🔍 DEBUG: Envelope keys: %v", getMapKeys(envelopeMap))

		// Use "Body" instead of "soapenv:Body" (namespace stripped)
		var body interface{}
		if body, exists = envelopeMap["Body"]; exists {
			log.Printf("🔍 DEBUG: Found Body (namespace stripped)")
		} else {
			log.Printf("❌ ERROR: No Body found in Envelope")
			return fareInfo
		}

		if bodyMap, ok := body.(map[string]interface{}); ok {
			log.Printf("🔍 DEBUG: Body keys: %v", getMapKeys(bodyMap))

			if reply, exists := bodyMap["Fare_InformativePricingWithoutPNRReply"]; exists {
				if replyMap, ok := reply.(map[string]interface{}); ok {
					log.Printf("✅ Found Fare_InformativePricingWithoutPNRReply")
					log.Printf("🔍 DEBUG: Reply keys: %v", getMapKeys(replyMap))

					// Extract from mainGroup -> pricingGroupLevelGroup -> fareInfoGroup
					if mainGroup, exists := replyMap["mainGroup"]; exists {
						if mainGroupMap, ok := mainGroup.(map[string]interface{}); ok {
							log.Printf("🔍 DEBUG: MainGroup keys: %v", getMapKeys(mainGroupMap))

							if pricingGroup, exists := mainGroupMap["pricingGroupLevelGroup"]; exists {
								if pricingGroupMap, ok := pricingGroup.(map[string]interface{}); ok {
									log.Printf("🔍 DEBUG: PricingGroup keys: %v", getMapKeys(pricingGroupMap))

									if fareInfoGroup, exists := pricingGroupMap["fareInfoGroup"]; exists {
										if fareInfoMap, ok := fareInfoGroup.(map[string]interface{}); ok {
											log.Printf("🔍 DEBUG: FareInfoGroup keys: %v", getMapKeys(fareInfoMap))

											// Extract fare amounts
											if fareAmount, exists := fareInfoMap["fareAmount"]; exists {
												log.Printf("🔍 DEBUG: Found fareAmount, calling extractFareAmounts")
												extractFareAmounts(fareAmount, &fareInfo)
											} else {
												log.Printf("❌ ERROR: No fareAmount in fareInfoGroup")
											}

											// Extract fare family
											if fareFamilyDetails, exists := fareInfoMap["fareFamilyDetails"]; exists {
												extractFareFamilyInfo(fareFamilyDetails, &fareInfo)
											}

											// Extract segment level information
											if segmentLevelGroup, exists := fareInfoMap["segmentLevelGroup"]; exists {
												extractSegmentInfo(segmentLevelGroup, &fareInfo)
											}
										} else {
											log.Printf("❌ ERROR: fareInfoGroup is not map[string]interface{}")
										}
									} else {
										log.Printf("❌ ERROR: No fareInfoGroup in pricingGroup")
									}
								} else {
									log.Printf("❌ ERROR: pricingGroup is not map[string]interface{}")
								}
							} else {
								log.Printf("❌ ERROR: No pricingGroupLevelGroup in mainGroup")
							}
						} else {
							log.Printf("❌ ERROR: mainGroup is not map[string]interface{}")
						}
					} else {
						log.Printf("❌ ERROR: No mainGroup in reply")
					}
				} else {
					log.Printf("❌ ERROR: reply is not map[string]interface{}")
				}
			} else {
				log.Printf("❌ ERROR: No Fare_InformativePricingWithoutPNRReply in body")
			}
		} else {
			log.Printf("❌ ERROR: body is not map[string]interface{}")
		}
	} else {
		log.Printf("❌ ERROR: envelope is not map[string]interface{}")
	}

	// Extract tax information from surchargesGroup
	if reply, exists := parsedXML["Envelope"].(map[string]interface{})["Body"].(map[string]interface{})["Fare_InformativePricingWithoutPNRReply"]; exists {
		if replyMap, ok := reply.(map[string]interface{}); ok {
			if mainGroup, exists := replyMap["mainGroup"]; exists {
				if mainGroupMap, ok := mainGroup.(map[string]interface{}); ok {
					if pricingGroup, exists := mainGroupMap["pricingGroupLevelGroup"]; exists {
						if pricingGroupMap, ok := pricingGroup.(map[string]interface{}); ok {
							if fareInfoGroup, exists := pricingGroupMap["fareInfoGroup"]; exists {
								if fareInfoMap, ok := fareInfoGroup.(map[string]interface{}); ok {
									// Extract tax details
									if surchargesGroup, exists := fareInfoMap["surchargesGroup"]; exists {
										extractTaxDetails(surchargesGroup, &fareInfo)
									}

									// Extract fare rules from textData
									extractFareRules(fareInfoMap, &fareInfo)

									// Extract segment level information including baggage and flight times
									if segmentLevelGroup, exists := fareInfoMap["segmentLevelGroup"]; exists {
										extractBaggageAllowance(segmentLevelGroup, &fareInfo)
										extractFlightTimes(segmentLevelGroup, &fareInfo)
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// Validate arithmetic: GrossFare - NetFare should equal sum of taxes
	calculatedTaxes := fareInfo.GrossAmount - fareInfo.NetAmount
	log.Printf("🔍 Tax Validation: GrossFare(%d) - NetFare(%d) = %d",
		fareInfo.GrossAmount, fareInfo.NetAmount, calculatedTaxes)

	log.Printf("🔍 Extracted FareInfo: BaseFare=%d, GrossAmount=%d, NetAmount=%d, FareClass=%s",
		fareInfo.BaseFare, fareInfo.GrossAmount, fareInfo.NetAmount, fareInfo.FareClass)
	log.Printf("🔍 Enhanced Data: BaggageAllowance=%s, FareRules=%d, TaxDetails=%d",
		fareInfo.BaggageAllowance, len(fareInfo.FareRules), len(fareInfo.TaxDetails))
	return fareInfo
}

// extractTaxDetails extracts tax information from surchargesGroup
func extractTaxDetails(surchargesGroup interface{}, fareInfo *FareInfo) {
	if surchargesMap, ok := surchargesGroup.(map[string]interface{}); ok {
		if taxesAmount, exists := surchargesMap["taxesAmount"]; exists {
			if taxesMap, ok := taxesAmount.(map[string]interface{}); ok {
				if taxDetails, exists := taxesMap["taxDetails"]; exists {
					// Handle both single tax and array of taxes
					switch taxDetailsType := taxDetails.(type) {
					case []interface{}:
						// Multiple taxes
						totalTaxes := 0
						var taxDetailsList []TaxDetail

						for _, tax := range taxDetailsType {
							if taxMap, ok := tax.(map[string]interface{}); ok {
								taxDetail := TaxDetail{}

								// Extract tax amount
								if rate, exists := taxMap["rate"]; exists {
									if rateStr, ok := rate.(string); ok {
										if taxAmount, err := strconv.Atoi(rateStr); err == nil {
											taxDetail.Amount = taxAmount
											totalTaxes += taxAmount
										}
									}
								}

								// Extract tax code (countryCode in Amadeus XML)
								if countryCode, exists := taxMap["countryCode"]; exists {
									if codeStr, ok := countryCode.(string); ok {
										taxDetail.Code = codeStr
									}
								}

								// Extract tax type
								if taxType, exists := taxMap["type"]; exists {
									if typeStr, ok := taxType.(string); ok {
										taxDetail.Type = typeStr
									}
								}

								if taxDetail.Amount > 0 {
									taxDetailsList = append(taxDetailsList, taxDetail)
									log.Printf("✅ Extracted Tax: %s %s = %d INR", taxDetail.Code, taxDetail.Type, taxDetail.Amount)
								}
							}
						}

						fareInfo.Taxes = totalTaxes
						fareInfo.TaxDetails = taxDetailsList
						log.Printf("✅ Extracted Total Taxes: %d INR from %d tax components", totalTaxes, len(taxDetailsList))

					case map[string]interface{}:
						// Single tax
						taxDetail := TaxDetail{}

						if rate, exists := taxDetailsType["rate"]; exists {
							if rateStr, ok := rate.(string); ok {
								if taxAmount, err := strconv.Atoi(rateStr); err == nil {
									taxDetail.Amount = taxAmount
									fareInfo.Taxes = taxAmount
								}
							}
						}

						if countryCode, exists := taxDetailsType["countryCode"]; exists {
							if codeStr, ok := countryCode.(string); ok {
								taxDetail.Code = codeStr
							}
						}

						if taxType, exists := taxDetailsType["type"]; exists {
							if typeStr, ok := taxType.(string); ok {
								taxDetail.Type = typeStr
							}
						}

						if taxDetail.Amount > 0 {
							fareInfo.TaxDetails = []TaxDetail{taxDetail}
							log.Printf("✅ Extracted Single Tax: %s %s = %d INR", taxDetail.Code, taxDetail.Type, taxDetail.Amount)
						}
					}
				}
			}
		}
	}
}

// extractFareRules extracts fare rules and restrictions from textData elements
func extractFareRules(fareInfoMap map[string]interface{}, fareInfo *FareInfo) {
	var fareRules []string

	if textDataList, exists := fareInfoMap["textData"]; exists {
		switch textDataType := textDataList.(type) {
		case []interface{}:
			// Multiple textData elements
			for _, textData := range textDataType {
				if textDataMap, ok := textData.(map[string]interface{}); ok {
					if freeText, exists := textDataMap["freeText"]; exists {
						if freeTextStr, ok := freeText.(string); ok {
							// Filter out fare construction lines and keep only rules
							if !strings.Contains(freeTextStr, "DEL AI MAA") &&
								!strings.Contains(freeTextStr, "FARE FAMILIES") &&
								!strings.Contains(freeTextStr, "FARE FAMILY:") {
								fareRules = append(fareRules, freeTextStr)
							}
						}
					}
				}
			}
		case map[string]interface{}:
			// Single textData element
			if freeText, exists := textDataType["freeText"]; exists {
				if freeTextStr, ok := freeText.(string); ok {
					if !strings.Contains(freeTextStr, "DEL AI MAA") {
						fareRules = append(fareRules, freeTextStr)
					}
				}
			}
		}
	}

	fareInfo.FareRules = fareRules
	log.Printf("✅ Extracted %d fare rules", len(fareRules))
}

// extractBaggageAllowance extracts baggage allowance information from segmentLevelGroup
func extractBaggageAllowance(segmentLevelGroup interface{}, fareInfo *FareInfo) {
	if segmentMap, ok := segmentLevelGroup.(map[string]interface{}); ok {
		if baggageAllowance, exists := segmentMap["baggageAllowance"]; exists {
			if baggageMap, ok := baggageAllowance.(map[string]interface{}); ok {
				if baggageDetails, exists := baggageMap["baggageDetails"]; exists {
					if detailsMap, ok := baggageDetails.(map[string]interface{}); ok {
						// Extract free allowance
						if freeAllowance, exists := detailsMap["freeAllowance"]; exists {
							if allowanceStr, ok := freeAllowance.(string); ok {
								fareInfo.BaggageAllowance = allowanceStr + "kg"
								log.Printf("✅ Extracted Baggage Allowance: %s", fareInfo.BaggageAllowance)
							}
						}

						// Extract quantity code
						if quantityCode, exists := detailsMap["quantityCode"]; exists {
							if codeStr, ok := quantityCode.(string); ok {
								fareInfo.BaggageCode = codeStr
								log.Printf("✅ Extracted Baggage Code: %s", fareInfo.BaggageCode)
							}
						}
					}
				}
			}
		}
	}
}

// extractFlightTimes extracts departure and arrival times from segmentLevelGroup
func extractFlightTimes(segmentLevelGroup interface{}, fareInfo *FareInfo) {
	if segmentMap, ok := segmentLevelGroup.(map[string]interface{}); ok {
		// Extract from additionalInformation
		if additionalInfo, exists := segmentMap["additionalInformation"]; exists {
			if additionalMap, ok := additionalInfo.(map[string]interface{}); ok {
				if productDateTime, exists := additionalMap["productDateTimeDetails"]; exists {
					if dateTimeMap, ok := productDateTime.(map[string]interface{}); ok {
						// Extract departure date
						if departureDate, exists := dateTimeMap["departureDate"]; exists {
							if depDateStr, ok := departureDate.(string); ok {
								fareInfo.DepartureDate = depDateStr
								log.Printf("✅ Extracted Departure Date: %s", fareInfo.DepartureDate)
							}
						}

						// Extract arrival date
						if arrivalDate, exists := dateTimeMap["arrivalDate"]; exists {
							if arrDateStr, ok := arrivalDate.(string); ok {
								fareInfo.ArrivalDate = arrDateStr
								log.Printf("✅ Extracted Arrival Date: %s", fareInfo.ArrivalDate)
							}
						}
					}
				}
			}
		}

		// Extract from segmentInformation for flight times
		if segmentInfo, exists := segmentMap["segmentInformation"]; exists {
			if segmentInfoMap, ok := segmentInfo.(map[string]interface{}); ok {
				if flightDate, exists := segmentInfoMap["flightDate"]; exists {
					if flightDateMap, ok := flightDate.(map[string]interface{}); ok {
						if departureDate, exists := flightDateMap["departureDate"]; exists {
							if depDateStr, ok := departureDate.(string); ok {
								// Convert DDMMYY to readable format for times
								fareInfo.DepartureTime = convertAmadeusDateToTime(depDateStr)
								fareInfo.ArrivalTime = convertAmadeusDateToTime(depDateStr) // Same day flight
								log.Printf("✅ Extracted Flight Times: Dep=%s, Arr=%s", fareInfo.DepartureTime, fareInfo.ArrivalTime)
							}
						}
					}
				}
			}
		}
	}
}

// convertAmadeusDateToTime converts DDMMYY format to readable time (placeholder for actual time extraction)
func convertAmadeusDateToTime(dateStr string) string {
	// For now, return empty as actual flight times would need additional API calls
	// In production, you might extract from schedule data or use default times
	return ""
}

// extractFareAmounts extracts fare amounts from fareAmount XML element
func extractFareAmounts(fareAmount interface{}, fareInfo *FareInfo) {
	log.Printf("🔍 DEBUG: extractFareAmounts called with type: %T", fareAmount)

	if fareAmountMap, ok := fareAmount.(map[string]interface{}); ok {
		log.Printf("🔍 DEBUG: fareAmount keys: %v", getMapKeys(fareAmountMap))

		// Extract monetaryDetails (base fare - typeQualifier="B")
		if monetaryDetails, exists := fareAmountMap["monetaryDetails"]; exists {
			if detailsMap, ok := monetaryDetails.(map[string]interface{}); ok {
				if typeQualifier, exists := detailsMap["typeQualifier"]; exists && typeQualifier == "B" {
					if amount, exists := detailsMap["amount"]; exists {
						if amountStr, ok := amount.(string); ok {
							if baseFare, err := strconv.Atoi(amountStr); err == nil {
								fareInfo.BaseFare = baseFare
								fareInfo.NetAmount = baseFare
								log.Printf("✅ SUCCESS: Extracted Base Fare: %d", baseFare)
							}
						}
					}
				}
			}
		}

		// Extract otherMonetaryDetails (total fare - typeQualifier="712")
		if otherMonetaryDetails, exists := fareAmountMap["otherMonetaryDetails"]; exists {
			if detailsMap, ok := otherMonetaryDetails.(map[string]interface{}); ok {
				if typeQualifier, exists := detailsMap["typeQualifier"]; exists && typeQualifier == "712" {
					if amount, exists := detailsMap["amount"]; exists {
						if amountStr, ok := amount.(string); ok {
							if totalFare, err := strconv.Atoi(amountStr); err == nil {
								fareInfo.GrossAmount = totalFare
								log.Printf("✅ SUCCESS: Extracted Total Fare: %d", totalFare)
							}
						}
					}
				}
			}
		}
	}
}

// extractFareFamilyInfo extracts fare family information
func extractFareFamilyInfo(fareFamilyDetails interface{}, fareInfo *FareInfo) {
	if familyMap, ok := fareFamilyDetails.(map[string]interface{}); ok {
		if fareFamilyName, exists := familyMap["fareFamilyname"]; exists {
			if familyStr, ok := fareFamilyName.(string); ok {
				fareInfo.FareClass = familyStr
				log.Printf("✅ Extracted Fare Family: %s", familyStr)
			}
		}
	}
}

// extractSegmentInfo extracts segment level information
func extractSegmentInfo(segmentLevelGroup interface{}, fareInfo *FareInfo) {
	if segmentMap, ok := segmentLevelGroup.(map[string]interface{}); ok {
		// Extract flight identification
		if segmentInfo, exists := segmentMap["segmentInformation"]; exists {
			if segmentInfoMap, ok := segmentInfo.(map[string]interface{}); ok {
				if flightId, exists := segmentInfoMap["flightIdentification"]; exists {
					if flightIdMap, ok := flightId.(map[string]interface{}); ok {
						if flightNumber, exists := flightIdMap["flightNumber"]; exists {
							if flightNumStr, ok := flightNumber.(string); ok {
								fareInfo.FlightNumber = flightNumStr
								log.Printf("✅ Extracted Flight Number: %s", flightNumStr)
							}
						}
						if bookingClass, exists := flightIdMap["bookingClass"]; exists {
							if rbdStr, ok := bookingClass.(string); ok {
								fareInfo.RBD = rbdStr
								log.Printf("✅ Extracted RBD: %s", rbdStr)
							}
						}
					}
				}
			}
		}

		// Extract fare basis
		if fareBasis, exists := segmentMap["fareBasis"]; exists {
			if fareBasisMap, ok := fareBasis.(map[string]interface{}); ok {
				if additionalFareDetails, exists := fareBasisMap["additionalFareDetails"]; exists {
					if detailsMap, ok := additionalFareDetails.(map[string]interface{}); ok {
						if rateClass, exists := detailsMap["rateClass"]; exists {
							if fbcStr, ok := rateClass.(string); ok {
								fareInfo.FBC = fbcStr
								log.Printf("✅ Extracted FBC: %s", fbcStr)
							}
						}
					}
				}
			}
		}
	}
}

// generateFlightInfoFareRecommendations creates fare recommendations for FlightInfo API
func generateFlightInfoFareRecommendations(req FlightInfoServiceRequest, fareInfo FareInfo) []map[string]interface{} {
	provider := getProviderCode(req)
	flightNo := getFlightNumber(req)

	return []map[string]interface{}{
		{
			"JourneyKey": fmt.Sprintf("%s-%s,%s,%s,%s,E", req.From, req.To, flightNo, provider, req.OnwardDate),
			"Recommendations": []map[string]interface{}{
				{
					"Index":     fmt.Sprintf("%s|2", provider),
					"FareKey":   fmt.Sprintf("INR,S,%d", fareInfo.NetAmount+1097),
					"NetFare":   fareInfo.NetAmount + 1097,
					"RBD":       "S",
					"FareClass": "ECO CLASSIC",
					"FBC":       "SU1YWRII",
					"Amenities": nil,
				},
				{
					"Index":     fmt.Sprintf("%s|1", provider),
					"FareKey":   fmt.Sprintf("INR,T,%d", fareInfo.NetAmount),
					"NetFare":   fareInfo.NetAmount,
					"RBD":       fareInfo.RBD,
					"FareClass": fareInfo.FareClass,
					"FBC":       fareInfo.FBC,
					"Amenities": nil,
				},
			},
		},
	}
}

// generateFlightInfoTripsFromInformativePricing generates trip data from Fare_InformativePricingWithoutPNR response
func generateFlightInfoTripsFromInformativePricing(req FlightInfoServiceRequest, fareInfo FareInfo) []map[string]interface{} {
	provider := getProviderCode(req)
	flightNo := getFlightNumber(req)

	return []map[string]interface{}{
		{
			"Journey": []map[string]interface{}{
				{
					"Provider":        provider,
					"OrderID":         0,
					"Stops":           0,
					"Index":           fmt.Sprintf("%s|1", provider),
					"SPFareNotice":    "",
					"GrossFare":       fareInfo.GrossAmount,
					"NetFare":         fareInfo.NetAmount,
					"FareKey":         fmt.Sprintf("INR,T,%d", fareInfo.NetAmount),
					"JourneyKey":      fmt.Sprintf("%s-%s,%s,%s,%s,E", req.From, req.To, flightNo, provider, req.OnwardDate),
					"PaxCategory":     "ADT",
					"PrivateFarePCC":  "PR-3A78",
					"PrivateFareType": "PR",
					"Notices":         fareInfo.FareRules, // Added fare rules as notices
					"Segments": []map[string]interface{}{
						{
							"Flight": map[string]interface{}{
								"FUID":              0,
								"VAC":               provider,
								"MAC":               provider,
								"OAC":               provider,
								"FBC":               fareInfo.FBC,
								"Airline":           getAirlineName(provider),
								"AirCraft":          fareInfo.Aircraft,
								"ArrAirportName":    getAirportName(req.To),
								"ArrivalCode":       req.To,
								"ArrivalTerminal":   getTerminal(req.To, provider),
								"ArrivalTime":       fareInfo.ArrivalTime,
								"Cabin":             "E",
								"DepAirportName":    getAirportName(req.From),
								"DepartureCode":     req.From,
								"DepartureTerminal": getTerminal(req.From, provider),
								"DepartureTime":     fareInfo.DepartureTime,
								"Duration":          fareInfo.Duration,
								"EmissionsInGrams":  0,
								"EquipmentType":     getEquipmentType(fareInfo.Aircraft),
								"FCType":            "",
								"FareClass":         fareInfo.FareClass,
								"FlightNo":          flightNo,
								"Hops":              nil,
								"MACAirlineLogo":    fmt.Sprintf("\\Content\\Templates\\images\\AirlineLogo\\%s.jpg", provider),
								"OACAirlineLogo":    fmt.Sprintf("\\Content\\Templates\\images\\AirlineLogo\\%s.jpg", provider),
								"RBD":               fareInfo.RBD,
								"Refundable":        "N",
								"Seats":             9,
								"VACAirlineLogo":    fmt.Sprintf("\\Content\\Templates\\images\\AirlineLogo\\%s.jpg", provider),
								"Amenities":         nil,
								"BaggageAllowance":  fareInfo.BaggageAllowance, // Added baggage allowance
								"BaggageCode":       fareInfo.BaggageCode,      // Added baggage code
							},
							"Fares": map[string]interface{}{
								"PTCFare": []map[string]interface{}{
									generatePTCFareWithRealTaxes(fareInfo),
								},
								"GrossFare":                fareInfo.GrossAmount,
								"NetFare":                  float64(fareInfo.NetAmount),
								"TotalServiceTax":          25,
								"TotalBaseFare":            fareInfo.BaseFare,
								"TotalCommission":          245.02,
								"TotalMarkup":              25,
								"TotalTax":                 float64(fareInfo.Taxes) - 220.02,
								"TotalAddonDiscount":       0,
								"TotalAddonMarkup":         0,
								"TotalAgentMarkUp":         0,
								"TotalAtoCharge":           0,
								"TotalReissueCharge":       0,
								"TotalVATonServiceCharge":  0,
								"TotalVATonTransactionFee": 0,
								"DealKey":                  "*",
							},
						},
					},
				},
			},
		},
	}
}

// Helper functions for equipment type
func getEquipmentType(aircraft string) string {
	equipmentMap := map[string]string{
		"Airbus A320": "32N",
		"Airbus A321": "321",
		"Boeing 737":  "737",
		"Boeing 777":  "777",
		"Boeing 787":  "787",
	}

	if equipment, exists := equipmentMap[aircraft]; exists {
		return equipment
	}
	return "32N" // Default equipment type
}

// generatePTCFareWithRealTaxes creates PTCFare object using real Amadeus tax data
func generatePTCFareWithRealTaxes(fareInfo FareInfo) map[string]interface{} {
	ptcFare := map[string]interface{}{
		"PTC":                 "ADT",
		"Fare":                fareInfo.BaseFare,
		"GrossFare":           fareInfo.GrossAmount,
		"NetFare":             float64(fareInfo.NetAmount),
		"Tax":                 fareInfo.Taxes,
		"AddonDiscount":       0,
		"AddonMarkup":         0,
		"AgentMarkUp":         0,
		"AtoCharge":           0,
		"CMS":                 245.02, // Commission - business logic
		"Markup":              25,     // Markup - business logic
		"ReissueCharge":       0,
		"ST":                  25, // Service tax - business logic
		"VATonServiceCharge":  0,
		"VATonTransactionFee": 0,
		"API":                 0,
		"PSF":                 0,
		"YQ":                  0,
	}

	// Map real Amadeus taxes to specific fields based on tax code
	// Handle multiple YR taxes by summing them
	yrTotal := 0
	for _, tax := range fareInfo.TaxDetails {
		switch tax.Code {
		case "YR":
			yrTotal += tax.Amount // Sum all YR taxes (VA + VB)
		case "K3":
			ptcFare["K3"] = tax.Amount
		case "P2":
			ptcFare["OT"] = tax.Amount // P2 maps to OT (Airport Fee)
			ptcFare["OTT"] = "P2"      // OTT indicates the tax type
		case "IN":
			ptcFare["UD"] = tax.Amount // IN maps to UD (domestic tax)
		default:
			// For other taxes, they are included in the total Tax amount
			log.Printf("🔍 DEBUG: Unmapped tax code %s with amount %d", tax.Code, tax.Amount)
		}
	}

	// Set the total YR amount
	if yrTotal > 0 {
		ptcFare["YR"] = yrTotal
		log.Printf("✅ Combined YR taxes: %d INR", yrTotal)
	}

	// Ensure arithmetic consistency: NetFare should be close to Fare - taxes + markup
	calculatedNetFare := float64(fareInfo.BaseFare) + 25 - 0.02 // Fare + markup - small adjustment
	ptcFare["NetFare"] = calculatedNetFare

	log.Printf("✅ Generated PTCFare with real taxes: Total=%d, Individual taxes mapped", fareInfo.Taxes)
	return ptcFare
}

// extractAmadeusError extracts and logs Amadeus error details
func extractAmadeusError(errorGroup interface{}) {
	if errorMap, ok := errorGroup.(map[string]interface{}); ok {
		// Extract error code
		if errorDetails, exists := errorMap["errorOrWarningCodeDetails"]; exists {
			if errorDetailsMap, ok := errorDetails.(map[string]interface{}); ok {
				if errorInfo, exists := errorDetailsMap["errorDetails"]; exists {
					if errorInfoMap, ok := errorInfo.(map[string]interface{}); ok {
						if errorCode, exists := errorInfoMap["errorCode"]; exists {
							log.Printf("❌ AMADEUS ERROR CODE: %v", errorCode)
						}
						if errorCategory, exists := errorInfoMap["errorCategory"]; exists {
							log.Printf("❌ AMADEUS ERROR CATEGORY: %v", errorCategory)
						}
					}
				}
			}
		}

		// Extract error description
		if errorDesc, exists := errorMap["errorWarningDescription"]; exists {
			if errorDescMap, ok := errorDesc.(map[string]interface{}); ok {
				if freeText, exists := errorDescMap["freeText"]; exists {
					log.Printf("❌ AMADEUS ERROR MESSAGE: %v", freeText)
				}
			}
		}
	}
}

// buildSegmentGroupsForMultiSegment creates segment groups for multi-segment flights
func buildSegmentGroupsForMultiSegment(req FlightInfoServiceRequest, formattedDate string) string {
	flightNumbers := strings.Split(req.FlightNo, "~")
	bookingClasses := strings.Split(req.BookingClass, "~")

	// Handle single segment flight
	if len(flightNumbers) == 1 {
		return fmt.Sprintf(`
	<segmentGroup>
		<segmentInformation>
			<flightDate>
				<departureDate>%s</departureDate>
			</flightDate>
			<boardPointDetails>
				<trueLocationId>%s</trueLocationId>
			</boardPointDetails>
			<offpointDetails>
				<trueLocationId>%s</trueLocationId>
			</offpointDetails>
			<companyDetails>
				<marketingCompany>%s</marketingCompany>
			</companyDetails>
			<flightIdentification>
				<flightNumber>%s</flightNumber>
				<bookingClass>%s</bookingClass>
			</flightIdentification>
			<flightTypeDetails>
				<flightIndicator>1</flightIndicator>
			</flightTypeDetails>
			<itemNumber>1</itemNumber>
		</segmentInformation>
	</segmentGroup>`,
			formattedDate,
			req.From,
			req.To,
			getAirlineCode(req.Airline),
			req.FlightNo,
			req.BookingClass)
	}

	// Handle multi-segment flight (connecting flights)
	var segments []string

	// For connecting flights, we need to determine intermediate airports
	// Based on the actual response: COK-PNQ (2720) and PNQ-HYD (2650)
	if len(flightNumbers) == 2 && req.From == "COK" && req.To == "HYD" {
		// COK to PNQ segment
		segments = append(segments, fmt.Sprintf(`
	<segmentGroup>
		<segmentInformation>
			<flightDate>
				<departureDate>%s</departureDate>
			</flightDate>
			<boardPointDetails>
				<trueLocationId>COK</trueLocationId>
			</boardPointDetails>
			<offpointDetails>
				<trueLocationId>PNQ</trueLocationId>
			</offpointDetails>
			<companyDetails>
				<marketingCompany>%s</marketingCompany>
			</companyDetails>
			<flightIdentification>
				<flightNumber>%s</flightNumber>
				<bookingClass>%s</bookingClass>
			</flightIdentification>
			<flightTypeDetails>
				<flightIndicator>1</flightIndicator>
			</flightTypeDetails>
			<itemNumber>1</itemNumber>
		</segmentInformation>
	</segmentGroup>`,
			formattedDate,
			getAirlineCode(req.Airline),
			flightNumbers[0],
			getBookingClass(bookingClasses, 0)))

		// PNQ to HYD segment (next day)
		nextDayDate := getNextDayDate(formattedDate)
		segments = append(segments, fmt.Sprintf(`
	<segmentGroup>
		<segmentInformation>
			<flightDate>
				<departureDate>%s</departureDate>
			</flightDate>
			<boardPointDetails>
				<trueLocationId>PNQ</trueLocationId>
			</boardPointDetails>
			<offpointDetails>
				<trueLocationId>HYD</trueLocationId>
			</offpointDetails>
			<companyDetails>
				<marketingCompany>%s</marketingCompany>
			</companyDetails>
			<flightIdentification>
				<flightNumber>%s</flightNumber>
				<bookingClass>%s</bookingClass>
			</flightIdentification>
			<flightTypeDetails>
				<flightIndicator>1</flightIndicator>
			</flightTypeDetails>
			<itemNumber>2</itemNumber>
		</segmentInformation>
	</segmentGroup>`,
			nextDayDate,
			getAirlineCode(req.Airline),
			flightNumbers[1],
			getBookingClass(bookingClasses, 1)))
	}

	return strings.Join(segments, "")
}

// getBookingClass safely gets booking class by index
func getBookingClass(bookingClasses []string, index int) string {
	if index < len(bookingClasses) {
		return bookingClasses[index]
	}
	return bookingClasses[0] // Default to first booking class
}

// getNextDayDate converts DDMMYY to next day DDMMYY
func getNextDayDate(dateStr string) string {
	// Parse DDMMYY format
	if len(dateStr) != 6 {
		return dateStr
	}

	day, _ := strconv.Atoi(dateStr[:2])
	month, _ := strconv.Atoi(dateStr[2:4])
	year, _ := strconv.Atoi(dateStr[4:6])

	// Add one day
	date := time.Date(2000+year, time.Month(month), day, 0, 0, 0, 0, time.UTC)
	nextDay := date.AddDate(0, 0, 1)

	return fmt.Sprintf("%02d%02d%02d", nextDay.Day(), nextDay.Month(), nextDay.Year()%100)
}
