package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/yourorg/provider-service/core/amadeus"
)

// Trip represents a single trip segment
type Trip struct {
	From       string `json:"From" validate:"required"`
	To         string `json:"To" validate:"required"`
	OnwardDate string `json:"OnwardDate" validate:"required"`
	ReturnDate string `json:"ReturnDate,omitempty"`
	TUI        string `json:"TUI,omitempty"`
}

// Parameters represents additional search parameters
type Parameters struct {
	IsDirect    bool   `json:"IsDirect"`
	PaxCategory string `json:"PaxCategory,omitempty"`
	Refundable  string `json:"Refundable,omitempty"`
}

// SearchPayload represents the search portion of the request
type SearchPayload struct {
	ADT        int        `json:"ADT" validate:"min=0"` // Adult passengers
	CHD        int        `json:"CHD" validate:"min=0"` // Child passengers
	INF        int        `json:"INF" validate:"min=0"` // Infant passengers
	Cabin      string     `json:"Cabin,omitempty"`      // Cabin class (Y, C, F)
	Source     string     `json:"Source,omitempty"`
	Mode       string     `json:"Mode,omitempty"`
	ClientID   string     `json:"ClientID,omitempty"`
	FareType   string     `json:"FareType,omitempty"`
	SecType    string     `json:"SecType,omitempty"`
	TUI        string     `json:"TUI,omitempty"`
	Trips      []Trip     `json:"Trips" validate:"required,min=1"`
	Parameters Parameters `json:"Parameters,omitempty"`
}

// ProviderSearchPayload represents the complete JSON request payload
type ProviderSearchPayload struct {
	TUI    string        `json:"tui,omitempty"`
	Search SearchPayload `json:"search"`
}

// validatePayload performs basic validation on the request payload
func validatePayload(payload *ProviderSearchPayload) error {
	search := &payload.Search

	if len(search.Trips) == 0 {
		return fmt.Errorf("at least one trip is required")
	}

	totalPax := search.ADT + search.CHD + search.INF
	if totalPax == 0 {
		return fmt.Errorf("at least one passenger is required")
	}

	if totalPax > 9 {
		return fmt.Errorf("maximum 9 passengers allowed")
	}

	for i, trip := range search.Trips {
		if strings.TrimSpace(trip.From) == "" {
			return fmt.Errorf("trip %d: origin is required", i+1)
		}
		if strings.TrimSpace(trip.To) == "" {
			return fmt.Errorf("trip %d: destination is required", i+1)
		}
		if strings.TrimSpace(trip.OnwardDate) == "" {
			return fmt.Errorf("trip %d: departure date is required", i+1)
		}
		if len(trip.From) != 3 || len(trip.To) != 3 {
			return fmt.Errorf("trip %d: airport codes must be 3 characters", i+1)
		}
	}

	return nil
}

// Handler invoked at POST /api/Fare_MasterPricerTravelBoardSearch
func FareMasterPricerHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Received %s request to %s", r.Method, r.URL.Path)

	if r.Method != http.MethodPost {
		http.Error(w, "method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var payload ProviderSearchPayload
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		log.Printf("JSON decode error: %v", err)
		http.Error(w, "invalid JSON payload: "+err.Error(), http.StatusBadRequest)
		return
	}

	// Validate the payload
	if err := validatePayload(&payload); err != nil {
		log.Printf("Validation error: %v", err)
		http.Error(w, "validation error: "+err.Error(), http.StatusBadRequest)
		return
	}

	search := &payload.Search
	log.Printf("Processing search request: ADT=%d, CHD=%d, INF=%d, Trips=%d",
		search.ADT, search.CHD, search.INF, len(search.Trips))

	// Read Amadeus config from env
	endpoint := os.Getenv("AMADEUS_ENDPOINT")
	if endpoint == "" {
		log.Printf("Missing AMADEUS_ENDPOINT environment variable")
		http.Error(w, "missing AMADEUS_ENDPOINT env var", http.StatusInternalServerError)
		return
	}
	wsap := os.Getenv("AMADEUS_WSAP") // e.g. FMPTBQ_23_1_1A
	username := os.Getenv("AMADEUS_USERNAME")
	password := os.Getenv("AMADEUS_PASSWORD")
	office := os.Getenv("AMADEUS_OFFICE")

	if username == "" || password == "" || office == "" {
		log.Printf("Missing required Amadeus credentials")
		http.Error(w, "missing Amadeus credentials", http.StatusInternalServerError)
		return
	}

	// Create enhanced search request with passenger details
	searchReq := amadeus.SearchRequest{
		Origin:      search.Trips[0].From,
		Destination: search.Trips[0].To,
		DepartDate:  search.Trips[0].OnwardDate,
		ReturnDate:  search.Trips[0].ReturnDate,
		Passengers:  search.ADT + search.CHD + search.INF,
		Adults:      search.ADT,
		Children:    search.CHD,
		Infants:     search.INF,
		Cabin:       search.Cabin,
		Trips:       convertTrips(search.Trips),
		TUI: func() string {
			if payload.TUI != "" {
				return payload.TUI
			}
			return search.TUI
		}(),
	}

	log.Printf("Calling Amadeus API: %s -> %s on %s",
		searchReq.Origin, searchReq.Destination, searchReq.DepartDate)

	// Perform the search: Build XML, call Amadeus, get JSON response
	js, err := amadeus.SearchAndConvertToJSON(endpoint, wsap, username, password, office, searchReq)
	if err != nil {
		log.Printf("Amadeus API error: %v", err)
		if len(js) > 0 {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusBadGateway)
			w.Write(js)
			return
		}
		http.Error(w, "amadeus error: "+err.Error(), http.StatusBadGateway)
		return
	}

	log.Printf("Successfully processed search request, returning JSON response")
	w.Header().Set("Content-Type", "application/json")
	w.Write(js)
}

// convertTrips converts the API trips to internal format
func convertTrips(trips []Trip) []amadeus.TripSegment {
	segments := make([]amadeus.TripSegment, len(trips))
	for i, trip := range trips {
		segments[i] = amadeus.TripSegment{
			From:       trip.From,
			To:         trip.To,
			OnwardDate: trip.OnwardDate,
			ReturnDate: trip.ReturnDate,
		}
	}
	return segments
}
