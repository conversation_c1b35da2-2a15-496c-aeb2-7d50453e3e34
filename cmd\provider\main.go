package main

import (
	"bufio"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/yourorg/provider-service/api/handlers"
)

// loadEnv loads environment variables from .env file
func loadEnv() {
	file, err := os.Open(".env")
	if err != nil {
		log.Printf("Warning: Could not open .env file: %v", err)
		return
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			// Remove quotes if present
			if strings.HasPrefix(value, "\"") && strings.HasSuffix(value, "\"") {
				value = value[1 : len(value)-1]
			}
			os.Setenv(key, value)
		}
	}
}

func main() {
	fmt.Println("🚀 STARTING PROVIDER SERVICE 🚀")

	// Load environment variables from .env file
	loadEnv()

	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
	}

	fmt.Printf("🔧 Using port: %s\n", port)

	mux := http.NewServeMux()
	// Health check endpoint
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"ok","service":"provider-service","version":"1.0.0","endpoints":["/api/search","/api/farerule","/api/flightinfo","/api/ssr"]}`))
	})
	// Primary endpoint for flight search
	mux.HandleFunc("/api/search", handlers.FareMasterPricerHandler)

	// Main APIs using SOAP implementation (based on SAMPLE files)
	mux.HandleFunc("/api/farerule", handlers.FareRuleSOAPHandler)
	mux.HandleFunc("/api/flightinfo", handlers.FlightInfoSOAPHandler)
	mux.HandleFunc("/api/ssr", handlers.SSRSOAPHandler)

	srv := &http.Server{
		Addr:         ":" + port,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 60 * time.Second,
	}

	fmt.Printf("🌐 Provider API listening on %s\n", srv.Addr)
	fmt.Printf("🔗 Health endpoint: http://localhost:%s/\n", port)
	fmt.Printf("🔗 FareRule endpoint: http://localhost:%s/api/farerule\n", port)
	fmt.Printf("🔗 FlightInfo endpoint: http://localhost:%s/api/flightinfo\n", port)

	log.Printf("Provider API listening on %s", srv.Addr)
	if err := srv.ListenAndServe(); err != nil {
		log.Fatal(err)
	}
}
