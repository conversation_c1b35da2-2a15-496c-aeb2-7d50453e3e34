package amadeus

import (
	"bytes"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/clbanning/mxj"
)

// SOAPClient handles all Amadeus SOAP operations
type SOAPClient struct {
	Endpoint string
	Username string
	Password string
	Office   string
	Timeout  time.Duration
}

// ActionMap maps service names to SOAP actions
var ActionMap = map[string]string{
	"Fare_MasterPricerTravelBoardSearch": "FMPTBQ_23_1_1A",
	"Fare_InformativePricingWithoutPNR":  "TIPNRQ_18_1_1A",
	"TIPNRQ":                             "TIPNRQ_18_1_1A", // Short key for FlightInfo
	"FMPTBQ":                             "FMPTBQ_23_1_1A", // Short key for Search
	"Air_MultiAvailability":              "AMAAVQ_18_1_1A",
	"Fare_CheckRules":                    "FARQNR_07_1_1A",
	"Service_IntegratedPricing":          "TPCBRQ_18_1_1A",
	"PNR_AddMultiElements":               "PNRADD_22_1_1A",
	"MiniRule_GetFromRec":                "TMRXRR_18_1_1A",
}

// NewSOAPClient creates a new SOAP client
func NewSOAPClient(endpoint, username, password, office string) *SOAPClient {
	return &SOAPClient{
		Endpoint: strings.TrimRight(endpoint, "/"),
		Username: username,
		Password: password,
		Office:   office,
		Timeout:  35 * time.Second,
	}
}

// generateNonce creates a random nonce
func (c *SOAPClient) generateNonce() ([]byte, error) {
	nonce := make([]byte, 16)
	_, err := rand.Read(nonce)
	return nonce, err
}

// computePasswordDigest creates WS-Security password digest
// FIXED: Amadeus Postman style - SHA1( nonce + created + SHA1(password) )
func (c *SOAPClient) computePasswordDigest(nonce []byte, created, password string) string {
	// First, compute SHA1 of the password
	sha1Pass := sha1.Sum([]byte(password))

	// Then compute SHA1 of (nonce + created + SHA1(password))
	h := sha1.New()
	h.Write(nonce)
	h.Write([]byte(created))
	h.Write(sha1Pass[:]) // Use SHA1 of password, not raw password
	digest := h.Sum(nil)
	return base64.StdEncoding.EncodeToString(digest)
}

// buildWSSecurityHeader creates WS-Security header
func (c *SOAPClient) buildWSSecurityHeader() (string, error) {
	nonce, err := c.generateNonce()
	if err != nil {
		return "", err
	}

	nonceB64 := base64.StdEncoding.EncodeToString(nonce)
	created := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	passwordDigest := c.computePasswordDigest(nonce, created, c.Password)

	return fmt.Sprintf(`
		<wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" soapenv:mustUnderstand="1">
			<wsse:UsernameToken wsu:Id="UsernameToken-1">
				<wsse:Username>%s</wsse:Username>
				<wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">%s</wsse:Password>
				<wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">%s</wsse:Nonce>
				<wsu:Created>%s</wsu:Created>
			</wsse:UsernameToken>
		</wsse:Security>`, c.Username, passwordDigest, nonceB64, created), nil
}

// buildHostedUserHeader creates AMA_SecurityHostedUser header
func (c *SOAPClient) buildHostedUserHeader() string {
	return fmt.Sprintf(`
		<AMA_SecurityHostedUser xmlns="http://xml.amadeus.com/2010/06/Security_v1">
			<UserID AgentDutyCode="SU" RequestorType="U" PseudoCityCode="%s" POS_Type="1" />
		</AMA_SecurityHostedUser>`, c.Office)
}

// SendSOAPRequest sends a SOAP request to Amadeus
func (c *SOAPClient) SendSOAPRequest(serviceName, bodyXML string) ([]byte, error) {
	log.Printf("🔧 DEBUG: SendSOAPRequest called with serviceName: %s", serviceName)
	actionKey, exists := ActionMap[serviceName]
	if !exists {
		return nil, fmt.Errorf("unknown service name: %s", serviceName)
	}
	log.Printf("🔧 DEBUG: Mapped to actionKey: %s", actionKey)

	// Generate message ID
	msgID := fmt.Sprintf("urn:uuid:%s", generateUUID())

	// Build security headers
	wsSecurity, err := c.buildWSSecurityHeader()
	if err != nil {
		return nil, fmt.Errorf("failed to build WS-Security header: %v", err)
	}

	hostedUser := c.buildHostedUserHeader()

	// CRITICAL FIX: Do NOT append action key to endpoint URL
	// The endpoint should already end with /<WSAP>
	// Action key goes ONLY in SOAPAction and wsa:Action headers
	fullEndpoint := strings.TrimRight(c.Endpoint, "/")
	log.Printf("DEBUG: Base endpoint: %s", c.Endpoint)
	log.Printf("DEBUG: Action key: %s", actionKey)
	log.Printf("DEBUG: Full endpoint (FIXED - no action appended): %s", fullEndpoint)

	// Build complete SOAP envelope - FIXED structure for Amadeus
	envelope := fmt.Sprintf(`<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:add="http://www.w3.org/2005/08/addressing">
	<soapenv:Header>
		<add:MessageID>%s</add:MessageID>
		<add:Action>http://webservices.amadeus.com/%s</add:Action>
		<add:To>%s</add:To>
		<add:ReplyTo>
			<add:Address>http://www.w3.org/2005/08/addressing/anonymous</add:Address>
		</add:ReplyTo>
		%s
		%s
	</soapenv:Header>
	<soapenv:Body>
		%s
	</soapenv:Body>
</soapenv:Envelope>`, msgID, actionKey, fullEndpoint, wsSecurity, hostedUser, bodyXML)

	// Save SOAP request XML to file for debugging
	requestFileName := fmt.Sprintf("last_%s_request.xml", strings.ToLower(serviceName))
	if err := os.WriteFile(requestFileName, []byte(envelope), 0644); err != nil {
		log.Printf("Warning: Could not save request XML to %s: %v", requestFileName, err)
	} else {
		log.Printf("SOAP request saved to: %s", requestFileName)
	}

	// Log request for debugging
	log.Printf("Sending SOAP request to %s for service %s", c.Endpoint, serviceName)

	// Save request XML for debugging
	if err := c.saveXMLToFile([]byte(envelope), fmt.Sprintf("last_%s_request.xml", strings.ToLower(serviceName))); err != nil {
		log.Printf("Warning: Failed to save request XML: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", fullEndpoint, bytes.NewReader([]byte(envelope)))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	req.Header.Set("Content-Type", "text/xml; charset=utf-8")
	req.Header.Set("SOAPAction", fmt.Sprintf("http://webservices.amadeus.com/%s", actionKey))

	// Send request
	client := &http.Client{Timeout: c.Timeout}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// Save response XML for debugging
	if err := c.saveXMLToFile(responseBody, fmt.Sprintf("last_%s_response.xml", strings.ToLower(serviceName))); err != nil {
		log.Printf("Warning: Failed to save response XML: %v", err)
	}

	// Check HTTP status
	if resp.StatusCode >= 400 {
		return responseBody, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, resp.Status)
	}

	log.Printf("Received SOAP response from %s, status: %d", serviceName, resp.StatusCode)
	return responseBody, nil
}

// SendStatefulSOAPRequest sends a SOAP request with session information for stateful services like Fare_CheckRules
func (c *SOAPClient) SendStatefulSOAPRequest(serviceName, bodyXML, sessionId, securityToken string, sequenceNumber int) ([]byte, error) {
	actionKey, exists := ActionMap[serviceName]
	if !exists {
		return nil, fmt.Errorf("unknown service name: %s", serviceName)
	}

	// Generate message ID
	msgID := fmt.Sprintf("urn:uuid:%s", generateUUID())

	// Build security headers
	wsSecurity, err := c.buildWSSecurityHeader()
	if err != nil {
		return nil, fmt.Errorf("failed to build WS-Security header: %v", err)
	}

	// Build hosted user header
	hostedUser := fmt.Sprintf(`
		<AMA_SecurityHostedUser xmlns="http://xml.amadeus.com/2010/06/Security_v1">
			<UserID POS_Type="1" PseudoCityCode="%s" AgentDutyCode="SU" RequestorType="U"/>
		</AMA_SecurityHostedUser>`, c.Office)

	// Build session header for stateful requests
	sessionHeader := ""
	if sessionId != "" && securityToken != "" {
		sessionHeader = fmt.Sprintf(`
		<awsse:Session TransactionStatusCode="Continue" xmlns:awsse="http://xml.amadeus.com/2010/06/Session_v3">
			<awsse:SessionId>%s</awsse:SessionId>
			<awsse:SequenceNumber>%d</awsse:SequenceNumber>
			<awsse:SecurityToken>%s</awsse:SecurityToken>
		</awsse:Session>`, sessionId, sequenceNumber, securityToken)
	}

	// Build full endpoint URL
	fullEndpoint := fmt.Sprintf("%s/%s", strings.TrimRight(c.Endpoint, "/"), actionKey)

	// Build complete SOAP envelope with session support
	envelope := fmt.Sprintf(`<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
	<soap:Header>
		<add:MessageID xmlns:add="http://www.w3.org/2005/08/addressing">%s</add:MessageID>
		<add:Action xmlns:add="http://www.w3.org/2005/08/addressing">http://webservices.amadeus.com/%s</add:Action>
		<add:To xmlns:add="http://www.w3.org/2005/08/addressing">%s</add:To>
		<link:TransactionFlowLink xmlns:link="http://wsdl.amadeus.com/2010/06/ws/Link_v1"/>
		%s
		%s
		%s
	</soap:Header>
	<soap:Body>
		%s
	</soap:Body>
</soap:Envelope>`, msgID, actionKey, fullEndpoint, sessionHeader, wsSecurity, hostedUser, bodyXML)

	// Save SOAP request XML to file for debugging
	requestFileName := fmt.Sprintf("last_%s_request.xml", strings.ToLower(serviceName))
	if err := c.saveXMLToFile([]byte(envelope), requestFileName); err != nil {
		log.Printf("Warning: Could not save request XML to %s: %v", requestFileName, err)
	} else {
		log.Printf("SOAP request saved to: %s", requestFileName)
	}

	// Create HTTP client with timeout
	client := &http.Client{Timeout: c.Timeout}

	// Create HTTP request
	req, err := http.NewRequest("POST", fullEndpoint, strings.NewReader(envelope))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "text/xml; charset=utf-8")
	req.Header.Set("SOAPAction", fmt.Sprintf("http://webservices.amadeus.com/%s", actionKey))

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Save response XML for debugging
	if err := c.saveXMLToFile(responseBody, fmt.Sprintf("last_%s_response.xml", strings.ToLower(serviceName))); err != nil {
		log.Printf("Warning: Failed to save response XML: %v", err)
	}

	// Check HTTP status
	if resp.StatusCode >= 400 {
		return responseBody, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, resp.Status)
	}

	log.Printf("Received SOAP response from %s, status: %d", serviceName, resp.StatusCode)
	return responseBody, nil
}

// saveXMLToFile saves XML content to file for debugging
func (c *SOAPClient) saveXMLToFile(content []byte, filename string) error {
	return os.WriteFile(filename, content, 0644)
}

// generateUUID generates a simple UUID
func generateUUID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:16])
}

// ParseXMLResponse parses XML response to map
func (c *SOAPClient) ParseXMLResponse(xmlBytes []byte) (map[string]interface{}, error) {
	mv, err := mxj.NewMapXml(xmlBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse XML: %v", err)
	}
	return mv, nil
}

// ExtractSOAPFault extracts SOAP fault from response
func (c *SOAPClient) ExtractSOAPFault(xmlBytes []byte) (string, error) {
	mv, err := c.ParseXMLResponse(xmlBytes)
	if err != nil {
		return "", err
	}

	// Check for SOAP fault
	if fault, exists := mv["soap:Envelope.soap:Body.soap:Fault"]; exists {
		if faultMap, ok := fault.(map[string]interface{}); ok {
			if faultString, exists := faultMap["faultstring"]; exists {
				return fmt.Sprintf("%v", faultString), nil
			}
		}
	}

	return "", nil
}

// GetSessionFromResponse extracts session information from response
func (c *SOAPClient) GetSessionFromResponse(xmlBytes []byte) (string, error) {
	mv, err := c.ParseXMLResponse(xmlBytes)
	if err != nil {
		return "", err
	}

	// Extract session information (implementation depends on specific response structure)
	// This is a placeholder - actual implementation would depend on Amadeus response format
	if session, exists := mv["soap:Envelope.soap:Header.ses:Session"]; exists {
		if sessionMap, ok := session.(map[string]interface{}); ok {
			if sessionId, exists := sessionMap["ses:SessionId"]; exists {
				return fmt.Sprintf("%v", sessionId), nil
			}
		}
	}

	return "", nil
}
