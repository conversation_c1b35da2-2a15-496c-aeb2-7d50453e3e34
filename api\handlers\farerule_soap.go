package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/yourorg/provider-service/api/models"
	"github.com/yourorg/provider-service/core/amadeus"
)

// FareRuleSOAPHandler handles POST /api/farerule requests using SOAP Amadeus
func FareRuleSOAPHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("🚀 FareRuleSOAPHandler CALLED - Method: %s, URL: %s", r.Method, r.URL.Path)

	if r.Method != http.MethodPost {
		log.Printf("❌ Method not allowed: %s", r.Method)
		http.Error(w, "method not allowed", http.StatusMethodNotAllowed)
		return
	}

	log.Printf("✅ Processing POST request to FareRule API")

	log.Printf("📝 Parsing JSON request...")
	var request models.FareRuleRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		log.Printf("❌ JSON decode error: %v", err)
		http.Error(w, "invalid JSON payload: "+err.Error(), http.StatusBadRequest)
		return
	}
	log.Printf("✅ JSON parsed successfully: TUI=%s, From=%s, To=%s", request.TUI, request.From, request.To)

	// Validate required fields
	if request.From == "" || request.To == "" {
		log.Printf("Validation error: missing required fields")
		http.Error(w, "validation error: From and To are required", http.StatusBadRequest)
		return
	}

	// Set defaults if not provided
	if request.Provider == "" {
		request.Provider = "6E" // Default to IndiGo
	}
	if request.FUID == "" {
		request.FUID = "1"
	}
	if request.FareComponent == 0 {
		request.FareComponent = 1
	}
	if len(request.RuleCategories) == 0 {
		request.RuleCategories = []string{"PE", "MX", "SR", "VC", "VR"} // Default rule categories
	}

	log.Printf("Processing SOAP fare rule request: From=%s, To=%s, Provider=%s, FareComponent=%d",
		request.From, request.To, request.Provider, request.FareComponent)

	log.Printf("🔧 Reading Amadeus configuration...")
	// Read Amadeus config from env
	endpoint := os.Getenv("AMADEUS_ENDPOINT")
	if endpoint == "" {
		log.Printf("❌ Missing AMADEUS_ENDPOINT environment variable")
		http.Error(w, "missing AMADEUS_ENDPOINT env var", http.StatusInternalServerError)
		return
	}

	username := os.Getenv("AMADEUS_USERNAME")
	password := os.Getenv("AMADEUS_PASSWORD")
	office := os.Getenv("AMADEUS_OFFICE")

	if username == "" || password == "" || office == "" {
		log.Printf("❌ Missing required Amadeus credentials")
		http.Error(w, "missing Amadeus credentials", http.StatusInternalServerError)
		return
	}

	log.Printf("✅ Amadeus config loaded: endpoint=%s, office=%s", endpoint, office)

	// Create SOAP client
	log.Printf("🔧 Creating SOAP client...")
	soapClient := amadeus.NewSOAPClient(endpoint, username, password, office)
	log.Printf("✅ SOAP client created successfully")

	// Create fare rule service request with session support
	serviceReq := amadeus.FareRuleServiceRequest{
		TUI:      request.TUI,
		From:     request.From,
		To:       request.To,
		FlightNo: request.FlightNo,
		Provider: request.Provider,
		FUID:     request.FUID,
		FareKey:  request.FareKey,
		// Session information for stateful requests
		SessionId:      request.SessionId,
		SequenceNumber: request.SequenceNumber,
		SecurityToken:  request.SecurityToken,
		// Pricing reference information
		PricingRecordId:     request.PricingRecordId,
		FareComponentIds:    request.FareComponentIds,
		FareSolutionNumber:  1, // Default to first solution
		FareComponentNumber: request.FareComponent,
		RuleCategories:      request.RuleCategories,
	}

	log.Printf("Calling Amadeus SOAP FARQNR API: %s -> %s, Categories: %v",
		serviceReq.From, serviceReq.To, serviceReq.RuleCategories)

	// Process the fare rule request
	jsonResponse, err := amadeus.ProcessFareRuleRequest(soapClient, serviceReq)
	if err != nil {
		log.Printf("Amadeus SOAP Fare Rule API error: %v, returning default fare rules", err)

		// Return default fare rules when service fails
		defaultResponse := createDefaultFareRuleResponse(request)
		w.Header().Set("Content-Type", "application/json")
		w.Write(defaultResponse)
		return
	}

	log.Printf("Successfully processed SOAP fare rule request, returning JSON response")
	w.Header().Set("Content-Type", "application/json")
	w.Write(jsonResponse)
}

// createDefaultFareRuleResponse creates a default fare rule response when Amadeus is unavailable
func createDefaultFareRuleResponse(request models.FareRuleRequest) []byte {
	// Generate TUI if not provided
	tui := request.TUI
	if tui == "" {
		tui = fmt.Sprintf("ON24%s|%s|%s",
			generateRandomString(8),
			generateRandomString(36),
			time.Now().Format("20060102150405"))
	}

	// Set defaults
	provider := request.Provider
	if provider == "" {
		provider = "6E"
	}
	fuid := request.FUID
	if fuid == "" {
		fuid = "1"
	}

	// Get default fare rules
	fareRules := models.GetDefaultFareRules()

	// Create response structure matching your exact format
	response := models.FareRuleResponse{
		TUI:  tui,
		Code: "200",
		Msg:  []string{"Success"},
		Trips: []models.FareTrip{
			{
				Journey: []models.FareJourney{
					{
						Provider:            provider,
						SpecialInformations: "",
						Segments: []models.FareSegment{
							{
								FUID: fuid,
								VAC:  provider,
								Rules: []models.FareRule{
									{
										Rule: fareRules,
									},
								},
							},
						},
					},
				},
			},
		},
	}

	// Convert to JSON
	jsonBytes, _ := json.MarshalIndent(response, "", "  ")
	return jsonBytes
}

// generateRandomString generates a random string of specified length
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(result)
}
