<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Header>
    <add:MessageID xmlns:add="http://www.w3.org/2005/08/addressing">urn:uuid:12345678-1234-1234-1234-123456789012</add:MessageID>
    <add:Action xmlns:add="http://www.w3.org/2005/08/addressing">http://webservices.amadeus.com/TIPNRQ_18_1_1A</add:Action>
    <add:To xmlns:add="http://www.w3.org/2005/08/addressing">https://noded5.test.webservices.amadeus.com</add:To>
    <link:TransactionFlowLink xmlns:link="http://wsdl.amadeus.com/2010/06/ws/Link_v1"/>
    <oas:Security xmlns:oas="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
                  xmlns:oas1="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
      <oas:UsernameToken oas1:Id="UsernameToken-1">
        <oas:Username>WSB2TTBS</oas:Username>
        <oas:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">BASE64_NONCE_HERE</oas:Nonce>
        <oas:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">PASSWORD_DIGEST_HERE</oas:Password>
        <oas1:Created>2025-08-20T15:43:41Z</oas1:Created>
      </oas:UsernameToken>
    </oas:Security>
    <sec:AMA_SecurityHostedUser xmlns:sec="http://xml.amadeus.com/2010/06/Security_v1">
      <sec:UserID AgentDutyCode="SU" POS_Type="1" RequestorType="U" PseudoCityCode="DELVS38BX"/>
    </sec:AMA_SecurityHostedUser>
  </soap:Header>
  <soap:Body>
    <Fare_InformativePricingWithoutPNR xmlns="http://xml.amadeus.com/TIPNRQ_18_1_1A">
      <!-- Number of units: passengers and recommendations -->
      <numberOfUnit>
        <unitNumberDetail>
          <numberOfUnits>1</numberOfUnits>
          <typeOfUnit>PX</typeOfUnit>
        </unitNumberDetail>
        <unitNumberDetail>
          <numberOfUnits>250</numberOfUnits>
          <typeOfUnit>RC</typeOfUnit>
        </unitNumberDetail>
      </numberOfUnit>
      
      <!-- Passenger reference -->
      <paxReference>
        <ptc>ADT</ptc>
        <traveller>
          <ref>1</ref>
        </traveller>
      </paxReference>
      
      <!-- Segment information -->
      <segmentGroup>
        <segmentInformation>
          <flightDate>
            <departureDate>210825</departureDate>
          </flightDate>
          <boardPointDetails>
            <trueLocationId>BOM</trueLocationId>
          </boardPointDetails>
          <offpointDetails>
            <trueLocationId>DEL</trueLocationId>
          </offpointDetails>
          <companyDetails>
            <marketingCompany>AI</marketingCompany>
          </companyDetails>
          <flightIdentification>
            <flightNumber>2986</flightNumber>
            <bookingClass>T</bookingClass>
          </flightIdentification>
        </segmentInformation>
      </segmentGroup>
      
      <!-- Pricing options -->
      <pricingOptionGroup>
        <pricingOptionKey>
          <pricingOptionKey>RP</pricingOptionKey>
        </pricingOptionKey>
        <pricingOptionKey>
          <pricingOptionKey>RU</pricingOptionKey>
        </pricingOptionKey>
        <pricingOptionKey>
          <pricingOptionKey>TAC</pricingOptionKey>
        </pricingOptionKey>
      </pricingOptionGroup>
      
      <!-- Currency conversion -->
      <conversionRate>
        <conversionRateDetail>
          <currency>INR</currency>
        </conversionRateDetail>
      </conversionRate>
    </Fare_InformativePricingWithoutPNR>
  </soap:Body>
</soap:Envelope>
