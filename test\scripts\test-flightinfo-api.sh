#!/bin/bash

# FlightInfo API Testing Script
# This script tests the FlightInfo API with various scenarios

BASE_URL="http://localhost:8001"
API_ENDPOINT="$BASE_URL/api/flightinfo/soap"

echo "🧪 FlightInfo API Testing Script"
echo "================================="
echo "Base URL: $BASE_URL"
echo "API Endpoint: $API_ENDPOINT"
echo ""

# Function to test API endpoint
test_api() {
    local test_name="$1"
    local json_data="$2"
    local expected_status="$3"
    
    echo "🔍 Testing: $test_name"
    echo "Request: $json_data"
    
    response=$(curl -s -w "\n%{http_code}" -X POST "$API_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d "$json_data")
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "HTTP Status: $http_code"
    
    if [ "$http_code" = "$expected_status" ]; then
        echo "✅ PASS: Expected status $expected_status"
    else
        echo "❌ FAIL: Expected $expected_status, got $http_code"
    fi
    
    # Pretty print JSON response if it's valid JSON
    if echo "$response_body" | jq . > /dev/null 2>&1; then
        echo "Response (formatted):"
        echo "$response_body" | jq .
    else
        echo "Response (raw):"
        echo "$response_body"
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

# Check if server is running
echo "🔍 Checking if server is running..."
health_check=$(curl -s -w "%{http_code}" "$BASE_URL/" -o /dev/null)
if [ "$health_check" != "200" ]; then
    echo "❌ Server is not running on $BASE_URL"
    echo "Please start the server with: go run cmd/provider/main.go"
    exit 1
fi
echo "✅ Server is running"
echo ""

# Test 1: Basic One-Way Flight
test_api "Basic One-Way Flight" '{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "Y"
}' "200"

# Test 2: With TUI and Specific Flight
test_api "With TUI and Specific Flight" '{
  "TUI": "ON90decf5e-9874-4a5a-bfd5-ed04fbbf8136|e091c216-6a3a-4ad5-be8a-b7d5cd544431|20250820154341",
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ReturnDate": "",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "Y",
  "FlightNo": "2986",
  "Provider": "AI"
}' "200"

# Test 3: Round Trip
test_api "Round Trip Flight" '{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ReturnDate": "2025-08-25",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "Y"
}' "200"

# Test 4: Multiple Passengers
test_api "Multiple Passengers" '{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ADT": 2,
  "CHD": 1,
  "INF": 1,
  "Cabin": "Y"
}' "200"

# Test 5: Business Class
test_api "Business Class" '{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ADT": 1,
  "Cabin": "C"
}' "200"

# Test 6: Different Route
test_api "Different Route (DEL-BOM)" '{
  "From": "DEL",
  "To": "BOM",
  "OnwardDate": "2025-08-21",
  "ADT": 1,
  "Cabin": "Y"
}' "200"

# Test 7: Error Test - Missing Required Fields
test_api "Error Test - Missing Required Fields" '{
  "From": "BOM"
}' "400"

# Test 8: Error Test - Invalid Date Format
test_api "Error Test - Invalid Date Format" '{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "invalid-date",
  "ADT": 1
}' "400"

echo "🎉 Testing Complete!"
echo ""
echo "📋 Summary:"
echo "- Check the results above for PASS/FAIL status"
echo "- Look for XML files in the current directory for debugging:"
echo "  - last_fare_informativepricingwithoutpnr_request.xml"
echo "  - last_fare_informativepricingwithoutpnr_response.xml"
echo ""
echo "📊 Expected Response Structure:"
echo "- TUI: Transaction Unique Identifier"
echo "- Code: '200' for success"
echo "- Msg: ['Success']"
echo "- ADT/CHD/INF: Passenger counts"
echo "- FareRecommendations: Array of fare options"
echo "- Trips: Array of journey details with flights and fares"
echo ""
echo "🔧 Troubleshooting:"
echo "- If all tests fail with HTTP 500: Check Amadeus credentials in .env"
echo "- If tests fail with HTTP 400: Check request format"
echo "- If no response: Check if server is running on port 8001"
