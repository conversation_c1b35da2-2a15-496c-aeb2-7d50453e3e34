<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:add="http://www.w3.org/2005/08/addressing">
	<soapenv:Header>
		<add:MessageID>urn:uuid:f71a1fd9-8192-077e-38d7-f2dfc7d32f6c</add:MessageID>
		<add:Action>http://webservices.amadeus.com/FARQNR_18_1_1A</add:Action>
		<add:To>https://noded5.test.webservices.amadeus.com/1ASIWTBSB2T</add:To>
		<add:ReplyTo>
			<add:Address>http://www.w3.org/2005/08/addressing/anonymous</add:Address>
		</add:ReplyTo>

		
		<wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" soapenv:mustUnderstand="1">
			<wsse:UsernameToken wsu:Id="UsernameToken-1">
				<wsse:Username>WSB2TTBS</wsse:Username>
				<wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">1p7HYjXkfogn8NSKkjFrEMz49lg=</wsse:Password>
				<wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">LLFG1JCKqPMMLFCpWGhLrQ==</wsse:Nonce>
				<wsu:Created>2025-09-09T05:47:17Z</wsu:Created>
			</wsse:UsernameToken>
		</wsse:Security>
		
		<AMA_SecurityHostedUser xmlns="http://xml.amadeus.com/2010/06/Security_v1">
			<UserID POS_Type="1" PseudoCityCode="DELVS38BX" AgentDutyCode="SU" RequestorType="U"/>
		</AMA_SecurityHostedUser>
	</soapenv:Header>
	<soapenv:Body>
		
<Fare_CheckRules xmlns="http://xml.amadeus.com/FARQNR_18_1_1A">
	<fareRule>
		<tarifFareRule>
			<ruleSectionId>PE</ruleSectionId>
			<ruleSectionId>MX</ruleSectionId>
			<ruleSectionId>SR</ruleSectionId>
			<ruleSectionId>AP</ruleSectionId>
			<ruleSectionId>FL</ruleSectionId>
			<ruleSectionId>VC</ruleSectionId>
			<ruleSectionId>VR</ruleSectionId>
		</tarifFareRule>
	</fareRule>
</Fare_CheckRules>
	</soapenv:Body>
</soapenv:Envelope>