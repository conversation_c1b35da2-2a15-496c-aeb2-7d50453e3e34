# Amadeus Services Comparison for API Implementation

## Overview

This document compares different Amadeus services and recommends the optimal approach for implementing the 3 APIs: FareRule, FlightInfo, and SSR.

## Service Comparison

### 1. Fare Rules API

| Service | SOAP Action | Pros | Cons | Recommendation |
|---------|-------------|------|------|----------------|
| **Fare_CheckRules** | FARQNQ_07_1_1A | ✅ Dedicated fare rules service<br>✅ Comprehensive rule categories<br>✅ Message function 712 support | ❌ Requires existing fare solution | **✅ RECOMMENDED** |
| Fare_InformativePricingWithoutPNR | TIPNRQ_18_1_1A | ✅ No PNR required<br>✅ Includes basic rules | ❌ Less detailed rule breakdown | Alternative |

**Verdict:** Use `Fare_CheckRules` for dedicated fare rules functionality.

### 2. Flight Info API

| Service | SOAP Action | Pros | Cons | Recommendation |
|---------|-------------|------|------|----------------|
| Enhanced MasterPricer | FMPTBQ_24_1_1A | ✅ Comprehensive search<br>✅ Multiple options<br>✅ Fare recommendations | ❌ Complex request structure<br>❌ May return too much data | Good for search |
| **Fare_InformativePricingWithoutPNR** | TIPNRQ_18_1_1A | ✅ Detailed pricing breakdown<br>✅ No PNR creation<br>✅ Specific flight pricing<br>✅ Includes fare rules | ❌ Requires flight details | **✅ RECOMMENDED** |

**Verdict:** Use `Fare_InformativePricingWithoutPNR` for detailed flight pricing information.

### 3. SSR API

| Service | SOAP Action | Pros | Cons | Recommendation |
|---------|-------------|------|------|----------------|
| Service_IntegratedPricing | TIPNRQ_18_1_1A | ✅ Dedicated SSR service | ❌ Limited to basic SSR | Alternative |
| **Fare_InformativePricingWithoutPNR** | TIPNRQ_18_1_1A | ✅ Comprehensive SSR with pricing<br>✅ Ancillary services<br>✅ No PNR required<br>✅ Integrated with fare data | ❌ More complex response | **✅ RECOMMENDED** |

**Verdict:** Use `Fare_InformativePricingWithoutPNR` with SSR options for comprehensive ancillary services.

## Recommended Architecture

### Current Implementation (Working)
```
/api/farerule    → Fare_CheckRules (FARQNQ_07_1_1A)
/api/flightinfo  → Enhanced MasterPricer (FMPTBQ_24_1_1A)
/api/ssr         → Service_IntegratedPricing (TIPNRQ_18_1_1A)
```

### Optimal Implementation (Recommended)
```
/api/farerule    → Fare_CheckRules (FARQNQ_07_1_1A)
/api/flightinfo  → Fare_InformativePricingWithoutPNR (TIPNRQ_18_1_1A)
/api/ssr         → Fare_InformativePricingWithoutPNR (TIPNRQ_18_1_1A) with SSR options
```

## Fare_InformativePricingWithoutPNR Benefits

### Key Advantages:
1. **No PNR Creation:** Get detailed pricing without booking
2. **Comprehensive Data:** Includes fare breakdown, taxes, fees, and rules
3. **SSR Integration:** Built-in ancillary services with pricing
4. **Flexibility:** Can be used for both flight info and SSR
5. **Real-time Pricing:** Current fare validation and pricing

### Request Structure:
```xml
<Fare_InformativePricingWithoutPNR>
    <passengersGroup>
        <segmentRepetitionControl>
            <segmentControlDetails>
                <quantity>1</quantity>
                <numberOfUnits>1</numberOfUnits>
            </segmentControlDetails>
        </segmentRepetitionControl>
        <paxReference>
            <ptc>ADT</ptc>
            <traveller><ref>1</ref></traveller>
        </paxReference>
    </passengersGroup>
    
    <!-- For SSR requests -->
    <pricingOptionGroup>
        <pricingOptionKey>
            <pricingOptionKey>SSR</pricingOptionKey>
        </pricingOptionKey>
    </pricingOptionGroup>
    
    <segmentGroup>
        <segmentInformation>
            <flightDate>
                <departureDate>190825</departureDate>
            </flightDate>
            <boardPointDetails>
                <trueLocationId>BOM</trueLocationId>
            </boardPointDetails>
            <offpointDetails>
                <trueLocationId>DEL</trueLocationId>
            </offpointDetails>
            <companyDetails>
                <marketingCompany>6E</marketingCompany>
            </companyDetails>
            <flightIdentification>
                <flightNumber>2007</flightNumber>
                <bookingClass>Y</bookingClass>
            </flightIdentification>
        </segmentInformation>
    </segmentGroup>
</Fare_InformativePricingWithoutPNR>
```

## Implementation Options

### Option 1: Keep Current Implementation (Stable)
- ✅ Already working and tested
- ✅ Uses dedicated services for each function
- ❌ May not provide optimal data integration

### Option 2: Migrate to Fare_InformativePricingWithoutPNR (Recommended)
- ✅ Better data integration
- ✅ More comprehensive pricing information
- ✅ Single service for multiple use cases
- ❌ Requires additional development and testing

### Option 3: Hybrid Approach (Flexible)
- Keep `Fare_CheckRules` for fare rules (dedicated service)
- Use `Fare_InformativePricingWithoutPNR` for flight info and SSR
- ✅ Best of both worlds
- ✅ Gradual migration possible

## Environment Configuration

### Current Configuration:
```env
AMADEUS_WSAP_FARERULES=FARQNQ_07_1_1A
AMADEUS_WSAP_FLIGHTINFO=FMPTBQ_24_1_1A
AMADEUS_WSAP_SSR=TIPNRQ_18_1_1A
```

### Recommended Configuration:
```env
AMADEUS_WSAP_FARERULES=FARQNQ_07_1_1A
AMADEUS_WSAP_FLIGHTINFO=TIPNRQ_18_1_1A
AMADEUS_WSAP_SSR=TIPNRQ_18_1_1A
AMADEUS_WSAP_INFORMATIVEPRICING=TIPNRQ_18_1_1A
```

## Migration Strategy

### Phase 1: Add New Handlers (Completed)
- ✅ Created `informativepricing.go`
- ✅ Created `flightinfo_v2.go` and `ssr_v2.go`
- ✅ Maintained backward compatibility

### Phase 2: Testing and Validation
1. Test new handlers with real Amadeus credentials
2. Compare response quality and data completeness
3. Validate performance and reliability

### Phase 3: Gradual Migration
1. Add new endpoints (e.g., `/api/flightinfo/v2`, `/api/ssr/v2`)
2. Run both versions in parallel
3. Migrate clients to new endpoints
4. Deprecate old endpoints

## Conclusion

**Recommendation:** Use `Fare_InformativePricingWithoutPNR` (TIPNRQ_18_1_1A) for both FlightInfo and SSR APIs as it provides:

1. **Better Integration:** Single service for comprehensive data
2. **No PNR Requirement:** Pricing without booking commitment
3. **SSR Support:** Built-in ancillary services
4. **Real-time Validation:** Current fare and availability data
5. **Cost Efficiency:** Fewer API calls for complete information

The current implementation works well, but migrating to `Fare_InformativePricingWithoutPNR` will provide more comprehensive and integrated data for your flight booking system.
