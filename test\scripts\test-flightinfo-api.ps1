# FlightInfo API Testing Script (PowerShell)
# This script tests the FlightInfo API with various scenarios

$BaseURL = "http://localhost:8001"
$APIEndpoint = "$BaseURL/api/flightinfo/soap"

Write-Host "🧪 FlightInfo API Testing Script" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host "Base URL: $BaseURL"
Write-Host "API Endpoint: $APIEndpoint"
Write-Host ""

# Function to test API endpoint
function Test-API {
    param(
        [string]$TestName,
        [string]$JsonData,
        [int]$ExpectedStatus
    )
    
    Write-Host "🔍 Testing: $TestName" -ForegroundColor Yellow
    Write-Host "Request: $JsonData"
    
    try {
        $response = Invoke-RestMethod -Uri $APIEndpoint -Method POST -ContentType "application/json" -Body $JsonData -ErrorAction Stop
        $statusCode = 200
        Write-Host "HTTP Status: $statusCode" -ForegroundColor Green
        
        if ($statusCode -eq $ExpectedStatus) {
            Write-Host "✅ PASS: Expected status $ExpectedStatus" -ForegroundColor Green
        } else {
            Write-Host "❌ FAIL: Expected $ExpectedStatus, got $statusCode" -ForegroundColor Red
        }
        
        Write-Host "Response:"
        $response | ConvertTo-Json -Depth 10 | Write-Host
        
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "HTTP Status: $statusCode" -ForegroundColor Red
        
        if ($statusCode -eq $ExpectedStatus) {
            Write-Host "✅ PASS: Expected status $ExpectedStatus" -ForegroundColor Green
        } else {
            Write-Host "❌ FAIL: Expected $ExpectedStatus, got $statusCode" -ForegroundColor Red
        }
        
        Write-Host "Error Response:"
        Write-Host $_.Exception.Message -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "----------------------------------------"
    Write-Host ""
}

# Check if server is running
Write-Host "🔍 Checking if server is running..." -ForegroundColor Yellow
try {
    $healthCheck = Invoke-RestMethod -Uri "$BaseURL/" -Method GET -ErrorAction Stop
    Write-Host "✅ Server is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Server is not running on $BaseURL" -ForegroundColor Red
    Write-Host "Please start the server with: go run cmd/provider/main.go" -ForegroundColor Red
    exit 1
}
Write-Host ""

# Test 1: Basic One-Way Flight
Test-API -TestName "Basic One-Way Flight" -JsonData @'
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "Y"
}
'@ -ExpectedStatus 200

# Test 2: With TUI and Specific Flight
Test-API -TestName "With TUI and Specific Flight" -JsonData @'
{
  "TUI": "ON90decf5e-9874-4a5a-bfd5-ed04fbbf8136|e091c216-6a3a-4ad5-be8a-b7d5cd544431|20250820154341",
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ReturnDate": "",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "Y",
  "FlightNo": "2986",
  "Provider": "AI"
}
'@ -ExpectedStatus 200

# Test 3: Round Trip
Test-API -TestName "Round Trip Flight" -JsonData @'
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ReturnDate": "2025-08-25",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "Y"
}
'@ -ExpectedStatus 200

# Test 4: Multiple Passengers
Test-API -TestName "Multiple Passengers" -JsonData @'
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ADT": 2,
  "CHD": 1,
  "INF": 1,
  "Cabin": "Y"
}
'@ -ExpectedStatus 200

# Test 5: Business Class
Test-API -TestName "Business Class" -JsonData @'
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ADT": 1,
  "Cabin": "C"
}
'@ -ExpectedStatus 200

# Test 6: Different Route
Test-API -TestName "Different Route (DEL-BOM)" -JsonData @'
{
  "From": "DEL",
  "To": "BOM",
  "OnwardDate": "2025-08-21",
  "ADT": 1,
  "Cabin": "Y"
}
'@ -ExpectedStatus 200

# Test 7: Error Test - Missing Required Fields
Test-API -TestName "Error Test - Missing Required Fields" -JsonData @'
{
  "From": "BOM"
}
'@ -ExpectedStatus 400

Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "- Check the results above for PASS/FAIL status"
Write-Host "- Look for XML files in the current directory for debugging:"
Write-Host "  - last_fare_informativepricingwithoutpnr_request.xml"
Write-Host "  - last_fare_informativepricingwithoutpnr_response.xml"
Write-Host ""
Write-Host "📊 Expected Response Structure:" -ForegroundColor Cyan
Write-Host "- TUI: Transaction Unique Identifier"
Write-Host "- Code: '200' for success"
Write-Host "- Msg: ['Success']"
Write-Host "- ADT/CHD/INF: Passenger counts"
Write-Host "- FareRecommendations: Array of fare options"
Write-Host "- Trips: Array of journey details with flights and fares"
Write-Host ""
Write-Host "🔧 Troubleshooting:" -ForegroundColor Cyan
Write-Host "- If all tests fail with HTTP 500: Check Amadeus credentials in .env"
Write-Host "- If tests fail with HTTP 400: Check request format"
Write-Host "- If no response: Check if server is running on port 8001"
