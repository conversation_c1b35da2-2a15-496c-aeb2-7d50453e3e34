package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"github.com/yourorg/provider-service/core/amadeus"
)

// SSRSOAPHandler handles POST /api/ssr requests using SOAP Amadeus
func SSRSOAPHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("🚀🚀🚀 SSR HANDLER CALLED 🚀🚀🚀 - Method: %s, URL: %s", r.Method, r.URL.Path)

	// Immediate test response to see if handler is called
	if r.URL.Query().Get("test") == "handler" {
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{"test": "handler_called", "status": "working"}`))
		return
	}

	if r.Method != http.MethodPost {
		log.Printf("❌ Method not allowed: %s", r.Method)
		http.Error(w, "method not allowed", http.StatusMethodNotAllowed)
		return
	}

	log.Printf("✅ Processing POST request to SSR API")

	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	// Parse the simplified JSON request format
	var request struct {
		TUI           string `json:"TUI"`
		Provider      string `json:"Provider"`
		Currency      string `json:"Currency"`
		From          string `json:"From"`
		To            string `json:"To"`
		OnwardDate    string `json:"OnwardDate"`
		ReturnDate    string `json:"ReturnDate"`
		ADT           int    `json:"ADT"`
		CHD           int    `json:"CHD"`
		INF           int    `json:"INF"`
		Cabin         string `json:"Cabin"`
		FlightNo      string `json:"FlightNo"`
		Airline       string `json:"Airline"`
		BookingClass  string `json:"BookingClass"`
		FareBasisCode string `json:"FareBasisCode"`
		FUID          int    `json:"FUID"`
		PTC           string `json:"PTC"`
		SSRList       []struct {
			Code        string `json:"Code"`
			Description string `json:"Description"`
		} `json:"SSRList"`
	}

	if err := json.Unmarshal(body, &request); err != nil {
		log.Printf("❌ Failed to parse JSON request: %v", err)
		http.Error(w, "Invalid JSON request", http.StatusBadRequest)
		return
	}

	log.Printf("📋 SSR Request parsed: %s->%s, SSR items: %d", request.From, request.To, len(request.SSRList))

	// Convert to SSR service request format
	ssrItems := make([]amadeus.SSRItem, len(request.SSRList))
	for i, item := range request.SSRList {
		ssrItems[i] = amadeus.SSRItem{
			Code:        item.Code,
			Description: item.Description,
		}
	}

	ssrRequest := amadeus.SSRServiceRequest{
		TUI:           request.TUI,
		Provider:      request.Provider,
		Currency:      request.Currency,
		From:          request.From,
		To:            request.To,
		DepartureDate: request.OnwardDate,
		ADT:           request.ADT,
		CHD:           request.CHD,
		INF:           request.INF,
		FlightNo:      request.FlightNo,
		FUID:          fmt.Sprintf("%d", request.FUID),
		PTC:           request.PTC,
		SSRList:       ssrItems,
	}

	// Create SOAP client
	soapURL := os.Getenv("AMADEUS_SOAP_URL")
	if soapURL == "" {
		soapURL = "https://nodeD1.test.webservices.amadeus.com/1ASIWTUTICO"
	}

	client := &amadeus.SOAPClient{
		Endpoint: soapURL,
		Username: os.Getenv("AMADEUS_USERNAME"),
		Password: os.Getenv("AMADEUS_PASSWORD"),
	}

	log.Printf("🔧 Calling Amadeus SSR service with URL: %s", soapURL)

	// Process SSR request
	jsonResponse, err := amadeus.ProcessSSRRequest(client, ssrRequest)
	if err != nil {
		log.Printf("❌ SSR processing failed: %v", err)

		// Create error response in expected format
		errorResponse := map[string]interface{}{
			"TUI":     request.TUI,
			"Code":    "500",
			"Msg":     []string{fmt.Sprintf("SSR API Error: %v", err)},
			"PaidSSR": false,
			"Trips":   []interface{}{},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(errorResponse)
		return
	}

	log.Printf("✅ SSR processing successful, response length: %d", len(jsonResponse))

	// Return JSON response
	w.Header().Set("Content-Type", "application/json")
	w.Write(jsonResponse)
}
