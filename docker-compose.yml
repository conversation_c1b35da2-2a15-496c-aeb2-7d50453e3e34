# services:
#   app:
#     build: .
#     ports:
#       - "8080:8080"
#     env_file:
#       - .env
#     environment:
#       - POSTGRES_DSN=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?sslmode=disable
#     depends_on:
#       postgres:
#         condition: service_healthy
#       redis:
#         condition: service_started

#   postgres:
#     image: postgres:15
#     environment:
#       POSTGRES_USER: ${POSTGRES_USER}
#       POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
#       POSTGRES_DB: ${POSTGRES_DB}
#     volumes:
#       - pgdata:/var/lib/postgresql/data
#     ports:
#       - "5432:5432"
#     healthcheck:
#       test: ["CMD-SHELL", "pg_isready -U $POSTGRES_USER -d $ "]
#       interval: 3s
#       timeout: 2s
#       retries: 5

#   redis:
#     image: redis:7
#     ports:
#       - "6379:6379"

#   migrate:
#     image: migrate/migrate
#     volumes:
#       - ./scripts/migrations:/migrations
#     env_file:
#       - .env
#     entrypoint:
#       - sh
#       - -c
#       - |
#         migrate -path=/migrations \
#           -database="postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?sslmode=disable" \
#           up
#     depends_on:
#       postgres:
#         condition: service_healthy

# volumes:
#   pgdata:




###########################################


version: '3.8'
services:
  provider:
    build: .
    ports:
      - "8000:8000"
    environment:
      - AMADEUS_ENDPOINT=${AMADEUS_ENDPOINT}
      - AMADEUS_WSAP=${AMADEUS_WSAP}
      - AMADEUS_USERNAME=${AMADEUS_USERNAME}
      - AMADEUS_PASSWORD=${AMADEUS_PASSWORD}
      - AMADEUS_OFFICE=${AMADEUS_OFFICE}
      - PORT=8000
    env_file:
      - .env
