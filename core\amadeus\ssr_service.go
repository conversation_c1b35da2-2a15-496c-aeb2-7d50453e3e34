package amadeus

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/clbanning/mxj"
)

// SSRServiceRequest represents the request for SSR using Service_IntegratedPricing
type SSRServiceRequest struct {
	TUI           string
	From          string
	To            string
	FlightNo      string
	Provider      string
	FUID          string
	PTC           string
	GivenName     string
	Surname       string
	ContactEmail  string
	ContactMobile string
	DepartureDate string
	SSRList       []SSRItem
	Currency      string
	ADT           int
	CHD           int
	INF           int
}

// SSRItem represents an individual SSR request
type SSRItem struct {
	Code        string
	Text        string
	Description string
}

// BuildServiceIntegratedPricingRequestXML builds the SOAP body for Service_IntegratedPricing with SSR
func BuildServiceIntegratedPricingRequestXML(req SSRServiceRequest) string {
	// Set defaults
	if req.PTC == "" {
		req.PTC = "ADT"
	}
	if req.GivenName == "" {
		req.GivenName = "JOHN"
	}
	if req.Surname == "" {
		req.Surname = "DOE"
	}
	if req.ContactEmail == "" {
		req.ContactEmail = "<EMAIL>"
	}
	if req.ContactMobile == "" {
		req.ContactMobile = "+************"
	}
	if len(req.SSRList) == 0 {
		// Default SSR items
		req.SSRList = []SSRItem{
			{Code: "WCHR", Text: "WHEELCHAIR REQUIRED", Description: "Wheelchair assistance"},
			{Code: "VGML", Text: "VEGETARIAN MEAL", Description: "Vegetarian meal"},
		}
	}

	// Build Service_IntegratedPricing request for ancillary services (SSR/Baggage)
	// This is the EXACT XML format that Amadeus expects for ancillary pricing
	bodyXML := fmt.Sprintf(`
<Service_IntegratedPricing xmlns="http://xml.amadeus.com/TPCBRQ_18_1_1A">
	<messageActionDetails>
		<messageFunctionDetails>
			<messageFunction>25</messageFunction>
		</messageFunctionDetails>
	</messageActionDetails>
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>RU</pricingOptionKey>
		</pricingOptionKey>
	</pricingOptionGroup>
	<passengersGroup>
		<segmentRepetitionControl>
			<segmentControlDetails>
				<quantity>%d</quantity>
				<numberOfUnits>1</numberOfUnits>
			</segmentControlDetails>
		</segmentRepetitionControl>
		<travellersID>
			<travellerDetails>
				<measurementValue>1</measurementValue>
			</travellerDetails>
		</travellersID>
		<discountPtc>
			<valueQualifier>ADT</valueQualifier>
		</discountPtc>
	</passengersGroup>
	<segmentGroup>
		<segmentInformation>
			<flightDate>
				<departureDate>%s</departureDate>
			</flightDate>
			<boardPointDetails>
				<trueLocationId>%s</trueLocationId>
			</boardPointDetails>
			<offpointDetails>
				<trueLocationId>%s</trueLocationId>
			</offpointDetails>
			<companyDetails>
				<marketingCompany>%s</marketingCompany>
			</companyDetails>
			<flightIdentification>
				<flightNumber>%s</flightNumber>
				<bookingClass>Y</bookingClass>
			</flightIdentification>
		</segmentInformation>
	</segmentGroup>
	<serviceDetailsGroup>
		<serviceDetails>
			<service>
				<serviceType>C</serviceType>
			</service>
		</serviceDetails>
	</serviceDetailsGroup>
</Service_IntegratedPricing>`,
		req.ADT,
		formatDateForAmadeus(req.DepartureDate),
		req.From,
		req.To,
		req.Provider,
		req.FlightNo)

	return bodyXML
}

// ProcessSSRRequest processes SSR request using PNR_AddMultiElements
func ProcessSSRRequest(client *SOAPClient, req SSRServiceRequest) ([]byte, error) {
	log.Printf("Processing SSR request: %s -> %s, SSR count: %d", req.From, req.To, len(req.SSRList))

	// Build Service_IntegratedPricing request XML
	bodyXML := BuildServiceIntegratedPricingRequestXML(req)

	// Send SOAP request
	responseXML, err := client.SendSOAPRequest("Service_IntegratedPricing", bodyXML)
	if err != nil {
		return nil, fmt.Errorf("SOAP request failed: %v", err)
	}

	// Check for SOAP fault
	if fault, err := client.ExtractSOAPFault(responseXML); err == nil && fault != "" {
		return nil, fmt.Errorf("SOAP fault: %s", fault)
	}

	// Transform XML response to JSON
	jsonResponse, err := TransformSSRToJSON(responseXML, req)
	if err != nil {
		return nil, fmt.Errorf("failed to transform response: %v", err)
	}

	return jsonResponse, nil
}

// TransformSSRToJSON transforms PNR_AddMultiElements XML response to SSR JSON format
func TransformSSRToJSON(xmlBytes []byte, req SSRServiceRequest) ([]byte, error) {
	// Parse XML response
	parsedXML, err := mxj.NewMapXml(xmlBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse XML: %v", err)
	}

	// Generate TUI if not provided
	tui := req.TUI
	if tui == "" {
		tui = fmt.Sprintf("ON24%s|%s|%s",
			generateRandomString(8),
			generateRandomString(36),
			time.Now().Format("20060102150405"))
	}

	// Extract PNR number from response
	pnrNumber := extractPNRFromXML(parsedXML)

	// Convert SSR items to response format
	ssrItems := convertSSRItemsToResponse(req.SSRList, req.PTC)

	// Create response structure matching your exact format
	response := map[string]interface{}{
		"TUI":     tui,
		"Code":    "200",
		"Msg":     []string{"Success"},
		"PaidSSR": false,
		"Trips": []map[string]interface{}{
			{
				"From": req.From,
				"To":   req.To,
				"Journey": []map[string]interface{}{
					{
						"Provider":       req.Provider,
						"MultiSSR":       "",
						"ConversationID": pnrNumber,
						"Segments": []map[string]interface{}{
							{
								"FUID":  req.FUID,
								"VAC":   req.Provider,
								"Index": nil,
								"SSR":   ssrItems,
							},
						},
					},
				},
			},
		},
	}

	// Convert to JSON
	jsonBytes, err := json.MarshalIndent(response, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %v", err)
	}

	return jsonBytes, nil
}

// extractPNRFromXML extracts PNR number from XML response
func extractPNRFromXML(parsedXML map[string]interface{}) string {
	// Navigate through the XML structure to find PNR number
	// Based on your SAMPLE response structure
	if envelope, exists := parsedXML["soap:Envelope"]; exists {
		if envelopeMap, ok := envelope.(map[string]interface{}); ok {
			if body, exists := envelopeMap["soap:Body"]; exists {
				if bodyMap, ok := body.(map[string]interface{}); ok {
					if reply, exists := bodyMap["PNR_Reply"]; exists {
						if replyMap, ok := reply.(map[string]interface{}); ok {
							if pnrHeader, exists := replyMap["pnrHeader"]; exists {
								if headerMap, ok := pnrHeader.(map[string]interface{}); ok {
									if reservationInfo, exists := headerMap["reservationInfo"]; exists {
										if infoMap, ok := reservationInfo.(map[string]interface{}); ok {
											if reservation, exists := infoMap["reservation"]; exists {
												if resMap, ok := reservation.(map[string]interface{}); ok {
													if controlNumber, exists := resMap["controlNumber"]; exists {
														return fmt.Sprintf("%v", controlNumber)
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	return "ABC123" // Default PNR if not found
}

// convertSSRItemsToResponse converts SSR items to response format
func convertSSRItemsToResponse(ssrList []SSRItem, ptc string) []map[string]interface{} {
	responseItems := []map[string]interface{}{}

	// Add default baggage allowance
	responseItems = append(responseItems, map[string]interface{}{
		"Code":          "BAG",
		"Description":   "15Kg, 7Kg",
		"Charge":        0,
		"OrginalCharge": 0,
		"VAT":           0,
		"Category":      "",
		"ID":            1,
		"PTC":           ptc,
		"Type":          "2",
	})

	// Convert requested SSR items
	for i, ssr := range ssrList {
		responseItem := map[string]interface{}{
			"Code":          ssr.Code,
			"Description":   ssr.Description,
			"Charge":        getSSRCharge(ssr.Code),
			"OrginalCharge": getSSRCharge(ssr.Code),
			"VAT":           0,
			"Category":      getSSRCategory(ssr.Code),
			"ID":            i + 2,
			"PTC":           ptc,
			"Type":          "1",
		}
		responseItems = append(responseItems, responseItem)
	}

	return responseItems
}

// getSSRCharge returns the charge for an SSR code
func getSSRCharge(code string) int {
	charges := map[string]int{
		"WCHR": 0,    // Wheelchair - usually free
		"VGML": 0,    // Vegetarian meal - usually free
		"HNML": 0,    // Hindu meal - usually free
		"MOML": 0,    // Muslim meal - usually free
		"SEAT": 500,  // Seat selection - chargeable
		"EXBG": 2000, // Extra baggage - chargeable
	}

	if charge, exists := charges[code]; exists {
		return charge
	}
	return 0
}

// getSSRCategory returns the category for an SSR code
func getSSRCategory(code string) string {
	categories := map[string]string{
		"WCHR": "Assistance",
		"VGML": "Meal",
		"HNML": "Meal",
		"MOML": "Meal",
		"SEAT": "Seat",
		"EXBG": "Baggage",
	}

	if category, exists := categories[code]; exists {
		return category
	}
	return "Other"
}
