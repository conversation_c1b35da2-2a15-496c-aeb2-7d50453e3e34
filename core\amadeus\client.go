package amadeus

import (
	"bytes"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	mathrand "math/rand"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/clbanning/mxj"
)

// TripSegment represents a single trip segment
type TripSegment struct {
	From       string
	To         string
	OnwardDate string
	ReturnDate string
}

// SearchRequest : enhanced search fields
type SearchRequest struct {
	Origin      string        // Primary origin (for backward compatibility)
	Destination string        // Primary destination (for backward compatibility)
	DepartDate  string        // Primary departure date (for backward compatibility)
	ReturnDate  string        // Primary return date (for backward compatibility)
	Passengers  int           // Total passengers (for backward compatibility)
	Adults      int           // Adult passengers
	Children    int           // Child passengers
	Infants     int           // Infant passengers
	Cabin       string        // Cabin class (Y, C, F)
	Trips       []TripSegment // Multiple trip segments for multi-city
	TUI         string        // Transaction Unique Identifier from request
}

// ---------------- helpers ----------------
func randBytes(n int) ([]byte, error) {
	b := make([]byte, n)
	_, err := rand.Read(b)
	return b, err
}

func generateMessageID() string {
	// a compact message id
	b, _ := randBytes(12)
	return fmt.Sprintf("WbsConsu-%x", b)
}

// generateRandomString generates a random string of specified length
func generateRandomString(length int) string {
	const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = chars[mathrand.Intn(len(chars))]
	}
	return string(result)
}

// base64EncodeBytes encodes bytes to base64 string
func base64EncodeBytes(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}

// computePasswordDigest: matches the Postman script approach:
// sha1Password := SHA1(password)
// finalDigest := Base64( SHA1( nonce + created + sha1Password ) )
func computePasswordDigest(password string, nonce []byte, created string) string {
	sha1Pass := sha1.Sum([]byte(password))
	combined := make([]byte, 0, len(nonce)+len(created)+len(sha1Pass))
	combined = append(combined, nonce...)
	combined = append(combined, []byte(created)...)
	combined = append(combined, sha1Pass[:]...)
	final := sha1.Sum(combined)
	return base64.StdEncoding.EncodeToString(final[:])
}

// formatDateForAmadeus: YYYY-MM-DD -> DDMMYY (Postman uses ddmmyy)
func formatDateForAmadeus(d string) string {
	if d == "" {
		return ""
	}
	if strings.Contains(d, "-") {
		parts := strings.Split(d, "-")
		if len(parts) >= 3 {
			yy := parts[0]
			if len(yy) >= 2 {
				yy = yy[len(yy)-2:]
			}
			return fmt.Sprintf("%s%s%s", parts[2], parts[1], yy)
		}
	}
	return d
}

// BuildFareMasterPricerEnvelope
func BuildFareMasterPricerEnvelope(username, password, office string, req SearchRequest) (string, string, error) {
	nonceBytes, err := randBytes(16)
	if err != nil {
		return "", "", err
	}
	// the pre-request script uses a random ASCII string as 'nonce' then base64-encodes it;
	// here we will use raw bytes and base64 encode them for the XML nonce. It's compatible.
	base64Nonce := base64.StdEncoding.EncodeToString(nonceBytes)
	created := time.Now().UTC().Format(time.RFC3339) // ISO with Z, compatible with Postman
	passwordDigest := computePasswordDigest(password, nonceBytes, created)

	// Handle backward compatibility
	adults := req.Adults
	children := req.Children
	infants := req.Infants

	// If using old format, convert to new format
	if adults == 0 && children == 0 && infants == 0 && req.Passengers > 0 {
		adults = req.Passengers
	}

	totalPax := adults + children + infants
	if totalPax == 0 {
		totalPax = 1
		adults = 1
	}

	// Build passenger references with proper passenger types
	paxRefs := ""
	paxRef := 1

	// Add adults
	for i := 0; i < adults; i++ {
		paxRefs += fmt.Sprintf(`<paxReference>
                <ptc>ADT</ptc>
                <traveller><ref>%d</ref></traveller>
            </paxReference>`, paxRef)
		paxRef++
	}

	// Add children
	for i := 0; i < children; i++ {
		paxRefs += fmt.Sprintf(`<paxReference>
                <ptc>CHD</ptc>
                <traveller><ref>%d</ref></traveller>
            </paxReference>`, paxRef)
		paxRef++
	}

	// Add infants
	for i := 0; i < infants; i++ {
		paxRefs += fmt.Sprintf(`<paxReference>
                <ptc>INF</ptc>
                <traveller><ref>%d</ref></traveller>
            </paxReference>`, paxRef)
		paxRef++
	}

	// Build itineraries - support both old and new format
	itins := ""

	if len(req.Trips) > 0 {
		// Use new multi-trip format with proper round-trip handling
		segmentRef := 1
		for _, trip := range req.Trips {
			// Outbound segment
			dep := formatDateForAmadeus(trip.OnwardDate)
			itins += fmt.Sprintf(`<itinerary>
                <requestedSegmentRef><segRef>%d</segRef></requestedSegmentRef>
                <departureLocalization><departurePoint><locationId>%s</locationId></departurePoint></departureLocalization>
                <arrivalLocalization><arrivalPointDetails><locationId>%s</locationId></arrivalPointDetails></arrivalLocalization>
                <timeDetails><firstDateTimeDetail><date>%s</date></firstDateTimeDetail></timeDetails>
            </itinerary>`, segmentRef, trip.From, trip.To, dep)
			segmentRef++

			// Return segment (if ReturnDate is provided)
			if trip.ReturnDate != "" {
				ret := formatDateForAmadeus(trip.ReturnDate)
				itins += fmt.Sprintf(`<itinerary>
                <requestedSegmentRef><segRef>%d</segRef></requestedSegmentRef>
                <departureLocalization><departurePoint><locationId>%s</locationId></departurePoint></departureLocalization>
                <arrivalLocalization><arrivalPointDetails><locationId>%s</locationId></arrivalPointDetails></arrivalLocalization>
                <timeDetails><firstDateTimeDetail><date>%s</date></firstDateTimeDetail></timeDetails>
            </itinerary>`, segmentRef, trip.To, trip.From, ret)
				segmentRef++
			}
		}
	} else {
		// Use old single-trip format for backward compatibility
		dep := formatDateForAmadeus(req.DepartDate)
		ret := formatDateForAmadeus(req.ReturnDate)

		itins = fmt.Sprintf(`<itinerary>
                <requestedSegmentRef><segRef>1</segRef></requestedSegmentRef>
                <departureLocalization><departurePoint><locationId>%s</locationId></departurePoint></departureLocalization>
                <arrivalLocalization><arrivalPointDetails><locationId>%s</locationId></arrivalPointDetails></arrivalLocalization>
                <timeDetails><firstDateTimeDetail><date>%s</date></firstDateTimeDetail></timeDetails>
            </itinerary>`, req.Origin, req.Destination, dep)

		if ret != "" {
			itins += fmt.Sprintf(`<itinerary>
                <requestedSegmentRef><segRef>2</segRef></requestedSegmentRef>
                <departureLocalization><departurePoint><locationId>%s</locationId></departurePoint></departureLocalization>
                <arrivalLocalization><arrivalPointDetails><locationId>%s</locationId></arrivalPointDetails></arrivalLocalization>
                <timeDetails><firstDateTimeDetail><date>%s</date></firstDateTimeDetail></timeDetails>
            </itinerary>`, req.Destination, req.Origin, ret)
		}
	}

	soapAction := "http://webservices.amadeus.com/FMPTBQ_23_1_1A"

	// envelope — conservative approach to request multiple flight options
	envelope := fmt.Sprintf(`<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope
    xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:sec="http://xml.amadeus.com/2010/06/Security_v1"
    xmlns:typ="http://xml.amadeus.com/2010/06/Types_v1"
    xmlns:iat="http://www.iata.org/IATA/2007/00/IATA2010.1"
    xmlns:app="http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3"
    xmlns:link="http://wsdl.amadeus.com/2010/06/ws/Link_v1"
    xmlns:ses="http://xml.amadeus.com/2010/06/Session_v3">
    <soap:Header xmlns:add="http://www.w3.org/2005/08/addressing">
        <add:MessageID>%s</add:MessageID>
        <add:Action>%s</add:Action>
        <add:To>%s/%s</add:To>
        <link:TransactionFlowLink />
        <oas:Security
            xmlns:oas="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:oas1="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <oas:UsernameToken oas1:Id="UsernameToken-1">
                <oas:Username>%s</oas:Username>
                <oas:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">%s</oas:Nonce>
                <oas:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">%s</oas:Password>
                <oas1:Created>%s</oas1:Created>
            </oas:UsernameToken>
        </oas:Security>
        <sec:AMA_SecurityHostedUser>
            <sec:UserID AgentDutyCode="SU" POS_Type="1" RequestorType="U" PseudoCityCode="%s" />
        </sec:AMA_SecurityHostedUser>
    </soap:Header>
    <soap:Body>
        <Fare_MasterPricerTravelBoardSearch>
            <numberOfUnit>
                <unitNumberDetail><numberOfUnits>20</numberOfUnits><typeOfUnit>RC</typeOfUnit></unitNumberDetail>
                <unitNumberDetail><numberOfUnits>%d</numberOfUnits><typeOfUnit>PX</typeOfUnit></unitNumberDetail>
            </numberOfUnit>
            %s

            <fareOptions>
                <pricingTickInfo>
                    <pricingTicketing>
                        <priceType>ET</priceType>
                        <priceType>RP</priceType>
                        <priceType>RU</priceType>
                        <priceType>RW</priceType>
                    </pricingTicketing>
                </pricingTickInfo>
            </fareOptions>
            %s
        </Fare_MasterPricerTravelBoardSearch>
    </soap:Body>
</soap:Envelope>`,
		generateMessageID(), soapAction, "%s", "%s",
		username, base64Nonce, passwordDigest, created, office, totalPax, paxRefs, itins)

	// we left two %s placeholders for endpoint/wsap to avoid escaping issues; caller will replace:
	// caller should call strings.Replace(envelope, "%s/%s", endpoint+"/"+wsap, 1)
	return envelope, soapAction, nil
}

// CallAmadeus posts the SOAP envelope to endpoint/wsap and returns raw XML bytes
func CallAmadeus(endpoint, wsap, envelope string, soapAction string) ([]byte, int, error) {
	target := strings.TrimRight(endpoint, "/")
	if wsap != "" {
		target = target + "/" + strings.TrimLeft(wsap, "/")
	}
	// inject endpoint/wsap into envelope header where placeholder exists:
	envelope = strings.Replace(envelope, "%s/%s", target, 1)

	client := &http.Client{Timeout: 40 * time.Second}
	req, err := http.NewRequest("POST", target, bytes.NewBufferString(envelope))
	if err != nil {
		return nil, 0, err
	}
	req.Header.Set("Content-Type", "text/xml;charset=UTF-8")
	req.Header.Set("SOAPAction", soapAction)

	resp, err := client.Do(req)
	if err != nil {
		return nil, 0, err
	}
	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)
	return body, resp.StatusCode, nil
}

// XMLToJSON uses mxj to convert arbitrary XML -> JSON
func XMLToJSON(xmlBytes []byte) ([]byte, error) {
	mv, err := mxj.NewMapXml(xmlBytes)
	if err != nil {
		return nil, err
	}
	js, err := mv.Json()
	if err != nil {
		return nil, err
	}
	var pretty bytes.Buffer
	_ = json.Indent(&pretty, js, "", "  ")
	return pretty.Bytes(), nil
}

// SearchAndConvertToJSON: high-level function used by handler
func SearchAndConvertToJSON(endpoint, wsap, username, password, office string, req SearchRequest) ([]byte, error) {
	envelopeTmpl, soapAction, err := BuildFareMasterPricerEnvelope(username, password, office, req)
	if err != nil {
		return nil, err
	}
	// inject endpoint/wsap
	envelope := strings.Replace(envelopeTmpl, "%s/%s", strings.TrimRight(endpoint, "/")+"/"+strings.TrimLeft(wsap, "/"), 1)

	// Save the XML request to last_request.xml
	err = saveXMLToFile([]byte(envelope), "last_request.xml")
	if err != nil {
		log.Printf("Warning: Failed to save XML request: %v", err)
	}

	raw, status, err := CallAmadeus(endpoint, wsap, envelope, soapAction)
	if err != nil {
		return nil, err
	}

	// Save the XML response to last_response.xml
	err = saveXMLToFile(raw, "last_response.xml")
	if err != nil {
		fmt.Printf("Warning: Failed to save XML response: %v\n", err)
	}
	if status >= 400 {
		// For errors, return simple error response in expected format
		errorResponse := fmt.Sprintf(`{
			"TUI": "ERROR_%d",
			"Code": "%d",
			"Completed": "True",
			"Msg": ["Amadeus API Error"],
			"Trips": []
		}`, time.Now().UnixNano(), status)
		return []byte(errorResponse), fmt.Errorf("amadeus returned status %d", status)
	}

	// Use the new transformer to convert to expected format
	js, err := TransformAmadeusResponse(raw, &req)
	if err != nil {
		// If transformation fails, return error instead of fallback
		return nil, fmt.Errorf("failed to transform response: %v", err)
	}
	return js, nil
}

// Helper functions for FlightInfo service

// getAirlineName returns airline name for common airline codes
func getAirlineName(code string) string {
	airlines := map[string]string{
		"6E": "IndiGo|IndiGo|IndiGo",
		"AI": "Air India|Air India|Air India",
		"SG": "SpiceJet|SpiceJet|SpiceJet",
		"UK": "Vistara|Vistara|Vistara",
		"G8": "GoAir|GoAir|GoAir",
	}

	if name, exists := airlines[code]; exists {
		return name
	}
	return fmt.Sprintf("%s|%s|%s", code, code, code)
}

// getAirportName returns airport name for common airport codes
func getAirportName(code string) string {
	airports := map[string]string{
		"BOM": "Chhatrapati Shivaji International airport |Mumbai |IN |India",
		"DEL": "Indira Gandhi International |New Delhi |IN |India",
		"HYD": "Rajiv Gandhi International Airport |Hyderabad |IN |India",
		"BLR": "Kempegowda International Airport |Bangalore |IN |India",
		"MAA": "Chennai International Airport |Chennai |IN |India",
		"CCU": "Netaji Subhash Chandra Bose International Airport |Kolkata |IN |India",
	}

	if name, exists := airports[code]; exists {
		return name
	}
	return fmt.Sprintf("%s Airport |%s |IN |India", code, code)
}

// getTerminal returns terminal for airport and airline combination
func getTerminal(airportCode, airlineCode string) string {
	// Terminal mapping based on common Indian airport configurations
	terminalMap := map[string]map[string]string{
		"BOM": {"AI": "2", "6E": "1", "SG": "1", "UK": "2"},
		"DEL": {"AI": "3", "6E": "1", "SG": "1", "UK": "3"},
		"HYD": {"AI": "1", "6E": "1", "SG": "1", "UK": "1"},
	}

	if airport, exists := terminalMap[airportCode]; exists {
		if terminal, exists := airport[airlineCode]; exists {
			return terminal
		}
	}
	return "1" // Default terminal
}



// saveXMLToFile saves XML data to a file
func saveXMLToFile(xmlData []byte, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create file %s: %v", filename, err)
	}
	defer file.Close()

	_, err = file.Write(xmlData)
	if err != nil {
		return fmt.Errorf("failed to write to file %s: %v", filename, err)
	}

	log.Printf("XML saved to %s (%d bytes)", filename, len(xmlData))
	return nil
}
