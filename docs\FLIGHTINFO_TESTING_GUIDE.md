# 🧪 FlightInfo API Testing Guide

## **📋 OVERVIEW**

This guide explains how to test the FlightInfo API that uses **Fare_InformativePricingWithoutPNR (TIPNRQ)** service.

**Flow:** JSON Request → XML (TIPNRQ) → Amadeus → XML Response → JSON Response

## **🎯 WHAT THE API DOES**

The FlightInfo API:
1. Accepts JSON request with flight search parameters
2. Converts to Amadeus TIPNRQ XML format
3. Sends SOAP request to Amadeus with WS-Security authentication
4. Receives XML response from Amadeus
5. Transforms XML to your specific JSON format
6. Returns detailed flight information with pricing

## **📊 REQUEST PARAMETERS**

### **Required Parameters:**
- `From` - Origin airport code (e.g., "BOM")
- `To` - Destination airport code (e.g., "DEL")  
- `OnwardDate` - Departure date in YYYY-MM-DD format

### **Optional Parameters:**
- `TUI` - Transaction Unique Identifier
- `ReturnDate` - Return date for round trip
- `ADT` - Number of adults (default: 1)
- `CHD` - Number of children (default: 0)
- `INF` - Number of infants (default: 0)
- `Cabin` - Cabin class ("Y" for Economy, "C" for Business, "F" for First)
- `FlightNo` - Specific flight number (e.g., "2986")
- `Provider` - Airline code (e.g., "AI" for Air India)

## **🔧 TESTING METHODS**

### **Method 1: Using Postman (Recommended)**

#### **Step 1: Setup Postman Collection**

1. **Open Postman**
2. **Create New Collection:** "Amadeus FlightInfo API"
3. **Add New Request:** "FlightInfo Test"

#### **Step 2: Configure Request**

**Request Settings:**
- **Method:** POST
- **URL:** `http://localhost:8001/api/flightinfo/soap`
- **Headers:**
  ```
  Content-Type: application/json
  Accept: application/json
  ```

**Request Body (JSON):**
```json
{
  "TUI": "ON90decf5e-9874-4a5a-bfd5-ed04fbbf8136|e091c216-6a3a-4ad5-be8a-b7d5cd544431|20250820154341",
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ReturnDate": "",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "Y",
  "FlightNo": "2986",
  "Provider": "AI"
}
```

#### **Step 3: Environment Setup**

Create Postman Environment with variables:
```
BASE_URL = http://localhost:8001
AMADEUS_ENDPOINT = https://noded5.test.webservices.amadeus.com
```

#### **Step 4: Execute Test**

1. **Start Your Service:**
   ```bash
   go run cmd/provider/main.go
   ```

2. **Send Request in Postman**
3. **Check Response Status:** Should be 200 for success, 502 for Amadeus errors
4. **Verify Response Format:** Should match your sample JSON structure

### **Method 2: Using cURL**

#### **Basic Test:**
```bash
curl -X POST http://localhost:8001/api/flightinfo/soap \
  -H "Content-Type: application/json" \
  -d '{
    "From": "BOM",
    "To": "DEL", 
    "OnwardDate": "2025-08-21",
    "ADT": 1,
    "Cabin": "Y"
  }'
```

#### **Complete Test with All Parameters:**
```bash
curl -X POST http://localhost:8001/api/flightinfo/soap \
  -H "Content-Type: application/json" \
  -d @test/postman-requests/flightinfo-test-request.json
```

### **Method 3: Using PowerShell (Windows)**

```powershell
$body = @{
    From = "BOM"
    To = "DEL"
    OnwardDate = "2025-08-21"
    ADT = 1
    CHD = 0
    INF = 0
    Cabin = "Y"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8001/api/flightinfo/soap" `
  -Method POST `
  -ContentType "application/json" `
  -Body $body
```

## **🔍 DEBUGGING & TROUBLESHOOTING**

### **Check XML Files**

Your implementation automatically saves XML files for debugging:
- `last_fare_informativepricingwithoutpnr_request.xml` - Request sent to Amadeus
- `last_fare_informativepricingwithoutpnr_response.xml` - Response from Amadeus

### **Common Issues & Solutions**

#### **1. HTTP 500 Error**
**Cause:** Invalid Amadeus credentials or network issues
**Solution:** 
- Check `.env` file has correct credentials
- Verify Amadeus endpoint is accessible
- Check XML request file for proper formatting

#### **2. HTTP 400 Error**
**Cause:** Invalid JSON request format
**Solution:**
- Verify required fields (From, To, OnwardDate)
- Check date format (YYYY-MM-DD)
- Validate airport codes (3-letter IATA codes)

#### **3. Empty Response**
**Cause:** No flights found for criteria
**Solution:**
- Try different dates
- Use popular routes (BOM-DEL, DEL-BOM)
- Check if airline operates on that route

### **Expected Response Structure**

```json
{
  "TUI": "ON90decf5e-9874-4a5a-bfd5-ed04fbbf8136|...",
  "Code": "200",
  "Msg": ["Success"],
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "CeilingInfo": "R0",
  "FareRecommendations": [{
    "JourneyKey": "BOM-DEL,2986,AI,2025-08-21,E",
    "Recommendations": [{
      "Index": "SB|2",
      "FareKey": "INR,S,5665",
      "NetFare": 5665,
      "RBD": "S",
      "FareClass": "ECO CLASSIC",
      "FBC": "SU1YWRII"
    }]
  }],
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "GrossAmount": 4813,
  "NetAmount": 4568,
  "SSRAmount": 0,
  "Trips": [{
    "Journey": [{
      "Provider": "AI",
      "GrossFare": 4813,
      "NetFare": 4568,
      "FareKey": "INR,T,4568",
      "JourneyKey": "BOM-DEL,2986,AI,2025-08-21,E",
      "Segments": [{
        "Flight": {
          "FlightNo": "2986",
          "Airline": "Air India|Air India|Air India",
          "DepartureCode": "BOM",
          "ArrivalCode": "DEL",
          "DepartureTime": "2025-08-21T00:50:00",
          "ArrivalTime": "2025-08-21T03:00:00",
          "Duration": "02h 10m",
          "AirCraft": "Airbus A320"
        },
        "Fares": {
          "GrossFare": 4813,
          "NetFare": 4568,
          "PTCFare": [{
            "PTC": "ADT",
            "Fare": 3904,
            "Tax": 884,
            "GrossFare": 4813,
            "NetFare": 4567.98
          }]
        }
      }]
    }]
  }]
}
```

## **📝 TEST SCENARIOS**

### **Scenario 1: Basic One-Way Flight**
```json
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ADT": 1
}
```

### **Scenario 2: Round Trip Flight**
```json
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ReturnDate": "2025-08-25",
  "ADT": 1
}
```

### **Scenario 3: Multiple Passengers**
```json
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "ADT": 2,
  "CHD": 1,
  "INF": 1
}
```

### **Scenario 4: Specific Flight**
```json
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "FlightNo": "2986",
  "Provider": "AI",
  "ADT": 1
}
```

### **Scenario 5: Business Class**
```json
{
  "From": "BOM",
  "To": "DEL",
  "OnwardDate": "2025-08-21",
  "Cabin": "C",
  "ADT": 1
}
```

## **🎯 VALIDATION CHECKLIST**

### **Request Validation:**
- [ ] Valid airport codes (3 letters, uppercase)
- [ ] Valid date format (YYYY-MM-DD)
- [ ] Future date (not past)
- [ ] Valid passenger counts (ADT + CHD + INF > 0)
- [ ] Valid cabin class (Y, C, F)

### **Response Validation:**
- [ ] HTTP Status 200 for success
- [ ] TUI matches request TUI
- [ ] Code = "200" for success
- [ ] Msg contains "Success"
- [ ] From/To match request
- [ ] OnwardDate matches request
- [ ] Passenger counts match request
- [ ] Trips array contains flight data
- [ ] Fare information is present
- [ ] Flight details are complete

## **🚀 PERFORMANCE TESTING**

### **Load Testing with Multiple Requests:**
```bash
# Test with different routes
for route in "BOM-DEL" "DEL-BOM" "BOM-BLR" "DEL-BLR"; do
  IFS='-' read -r from to <<< "$route"
  curl -X POST http://localhost:8001/api/flightinfo/soap \
    -H "Content-Type: application/json" \
    -d "{\"From\":\"$from\",\"To\":\"$to\",\"OnwardDate\":\"2025-08-21\",\"ADT\":1}"
  echo "Tested route: $route"
done
```

### **Concurrent Testing:**
```bash
# Run 5 concurrent requests
for i in {1..5}; do
  curl -X POST http://localhost:8001/api/flightinfo/soap \
    -H "Content-Type: application/json" \
    -d @test/postman-requests/flightinfo-test-request.json &
done
wait
```

## **📊 MONITORING & LOGGING**

### **Check Application Logs:**
```bash
# Monitor logs while testing
tail -f application.log
```

### **Monitor XML Files:**
```bash
# Watch for new XML files
ls -la last_fare_informativepricingwithoutpnr_*.xml
```

### **Check Response Times:**
```bash
# Measure response time
time curl -X POST http://localhost:8001/api/flightinfo/soap \
  -H "Content-Type: application/json" \
  -d @test/postman-requests/flightinfo-test-request.json
```

This comprehensive testing guide covers all aspects of testing your FlightInfo API from basic functionality to performance testing.
