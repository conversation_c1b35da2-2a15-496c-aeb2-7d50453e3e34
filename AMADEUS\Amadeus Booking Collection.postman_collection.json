{"info": {"_postman_id": "3d9a9fdb-b622-4218-8393-ffcf829e5fe9", "name": "Amadeus Booking Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "31842229"}, "item": [{"name": "Fare_MasterPricerTravelBoardSearch", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/FMPTBQ_23_1_1A", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/FMPTBQ_23_1_1A</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n        <oas:Security\r\n            xmlns:oas=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"\r\n            xmlns:oas1=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">\r\n            <oas:UsernameToken oas1:Id=\"UsernameToken-1\">\r\n                <oas:Username>{{username}}</oas:Username>\r\n                <oas:Nonce EncodingType=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary\">{{base64Nonce}}</oas:Nonce>\r\n                <oas:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest\">{{passwordDigest}}</oas:Password>\r\n                <oas1:Created>{{timestamp}}</oas1:Created>\r\n            </oas:UsernameToken>\r\n        </oas:Security>\r\n        <sec:AMA_SecurityHostedUser>\r\n            <sec:UserID AgentDutyCode=\"SU\" POS_Type=\"1\" RequestorType=\"U\" PseudoCityCode=\"{{officeId}}\" />\r\n        </sec:AMA_SecurityHostedUser>\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <Fare_MasterPricerTravelBoardSearch>\r\n            <numberOfUnit>\r\n                <unitNumberDetail>\r\n                    <numberOfUnits>1</numberOfUnits>\r\n                    <typeOfUnit>RC</typeOfUnit>\r\n                </unitNumberDetail>\r\n                <unitNumberDetail>\r\n                    <numberOfUnits>2</numberOfUnits>\r\n                    <typeOfUnit>PX</typeOfUnit>\r\n                </unitNumberDetail>\r\n            </numberOfUnit>\r\n            <paxReference>\r\n                <ptc>ADT</ptc>\r\n                <traveller>\r\n                    <ref>1</ref>\r\n                </traveller>\r\n            </paxReference>\r\n            <paxReference>\r\n                <ptc>ADT</ptc>\r\n                <traveller>\r\n                    <ref>2</ref>\r\n                </traveller>\r\n            </paxReference>\r\n            <fareOptions>\r\n                <pricingTickInfo>\r\n                    <pricingTicketing>\r\n                        <priceType>ET</priceType>\r\n                        <priceType>RP</priceType>\r\n                        <priceType>RU</priceType>\r\n                        <priceType>RW</priceType>\r\n                    </pricingTicketing>\r\n                </pricingTickInfo>\r\n            </fareOptions>\r\n            <itinerary>\r\n                <requestedSegmentRef>\r\n                    <segRef>1</segRef>\r\n                </requestedSegmentRef>\r\n                <departureLocalization>\r\n                    <departurePoint>\r\n                        <locationId>COK</locationId>\r\n                    </departurePoint>\r\n                </departureLocalization>\r\n                <arrivalLocalization>\r\n                    <arrivalPointDetails>\r\n                        <locationId>DEL</locationId>\r\n                    </arrivalPointDetails>\r\n                </arrivalLocalization>\r\n                <timeDetails>\r\n                    <firstDateTimeDetail>\r\n                        <date>200725</date>\r\n                    </firstDateTimeDetail>\r\n                </timeDetails>\r\n            </itinerary>\r\n            <itinerary>\r\n                <requestedSegmentRef>\r\n                    <segRef>2</segRef>\r\n                </requestedSegmentRef>\r\n                <departureLocalization>\r\n                    <departurePoint>\r\n                        <locationId>DEL</locationId>\r\n                    </departurePoint>\r\n                </departureLocalization>\r\n                <arrivalLocalization>\r\n                    <arrivalPointDetails>\r\n                        <locationId>COK</locationId>\r\n                    </arrivalPointDetails>\r\n                </arrivalLocalization>\r\n                <timeDetails>\r\n                    <firstDateTimeDetail>\r\n                        <date>280725</date>\r\n                    </firstDateTimeDetail>\r\n                </timeDetails>\r\n            </itinerary>\r\n        </Fare_MasterPricerTravelBoardSearch>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}, {"name": "Air_SellFromRecommendation", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// For debugging: console.log('SHA1 Password:', CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Concatenate nonce (plain, not base64) + timestamp + SHA1(password)\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/ITAREQ_05_2_IA", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <ses:Session TransactionStatusCode=\"Start\"/>\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/ITAREQ_05_2_IA</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n        <oas:Security\r\n            xmlns:oas=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"\r\n            xmlns:oas1=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">\r\n            <oas:UsernameToken oas1:Id=\"UsernameToken-1\">\r\n                <oas:Username>{{username}}</oas:Username>\r\n                <oas:Nonce EncodingType=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary\">{{base64Nonce}}</oas:Nonce>\r\n                <oas:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest\">{{passwordDigest}}</oas:Password>\r\n                <oas1:Created>{{timestamp}}</oas1:Created>\r\n            </oas:UsernameToken>\r\n        </oas:Security>\r\n        <sec:AMA_SecurityHostedUser>\r\n            <sec:UserID AgentDutyCode=\"SU\" POS_Type=\"1\" RequestorType=\"U\" PseudoCityCode=\"{{officeId}}\" />\r\n        </sec:AMA_SecurityHostedUser>\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <Air_SellFromRecommendation>\r\n            <messageActionDetails>\r\n                <messageFunctionDetails>\r\n                    <messageFunction>183</messageFunction>\r\n                    <additionalMessageFunction>M1</additionalMessageFunction>\r\n                </messageFunctionDetails>\r\n            </messageActionDetails>\r\n            <itineraryDetails>\r\n                <originDestinationDetails>\r\n                    <origin>COK</origin>\r\n                    <destination>DEL</destination>\r\n                </originDestinationDetails>\r\n                <message>\r\n                    <messageFunctionDetails>\r\n                        <messageFunction>183</messageFunction>\r\n                    </messageFunctionDetails>\r\n                </message>\r\n                <segmentInformation>\r\n                    <travelProductInformation>\r\n                        <flightDate>\r\n                            <departureDate>200725</departureDate>\r\n                        </flightDate>\r\n                        <boardPointDetails>\r\n                            <trueLocationId>COK</trueLocationId>\r\n                        </boardPointDetails>\r\n                        <offpointDetails>\r\n                            <trueLocationId>DEL</trueLocationId>\r\n                        </offpointDetails>\r\n                        <companyDetails>\r\n                            <marketingCompany>AI</marketingCompany>\r\n                        </companyDetails>\r\n                        <flightIdentification>\r\n                            <flightNumber>832</flightNumber>\r\n                            <bookingClass>M</bookingClass>\r\n                        </flightIdentification>\r\n                    </travelProductInformation>\r\n                    <relatedproductInformation>\r\n                        <quantity>3</quantity>\r\n                        <statusCode>NN</statusCode>\r\n                    </relatedproductInformation>\r\n                </segmentInformation>\r\n            </itineraryDetails>\r\n            <itineraryDetails>\r\n                <originDestinationDetails>\r\n                    <origin>DEL</origin>\r\n                    <destination>COK</destination>\r\n                </originDestinationDetails>\r\n                <message>\r\n                    <messageFunctionDetails>\r\n                        <messageFunction>183</messageFunction>\r\n                    </messageFunctionDetails>\r\n                </message>\r\n                <segmentInformation>\r\n                    <travelProductInformation>\r\n                        <flightDate>\r\n                            <departureDate>280725</departureDate>\r\n                        </flightDate>\r\n                        <boardPointDetails>\r\n                            <trueLocationId>DEL</trueLocationId>\r\n                        </boardPointDetails>\r\n                        <offpointDetails>\r\n                            <trueLocationId>COK</trueLocationId>\r\n                        </offpointDetails>\r\n                        <companyDetails>\r\n                            <marketingCompany>AI</marketingCompany>\r\n                        </companyDetails>\r\n                        <flightIdentification>\r\n                            <flightNumber>2885</flightNumber>\r\n                            <bookingClass>M</bookingClass>\r\n                        </flightIdentification>\r\n                    </travelProductInformation>\r\n                    <relatedproductInformation>\r\n                        <quantity>3</quantity>\r\n                        <statusCode>NN</statusCode>\r\n                    </relatedproductInformation>\r\n                </segmentInformation>\r\n            </itineraryDetails>\r\n        </Air_SellFromRecommendation>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}, {"name": "PNR_AddMultiElements", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// For debugging: console.log('SHA1 Password:', CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Concatenate nonce (plain, not base64) + timestamp + SHA1(password)\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/PNRADD_21_1_1A", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <ses:Session TransactionStatusCode=\"InSeries\">\r\n            <ses:SessionId>05GL6KYWVY</ses:SessionId>\r\n            <ses:SequenceNumber>2</ses:SequenceNumber>\r\n            <ses:SecurityToken>2G1IR8IBVRUTA3O1RKJ1MHBETX</ses:SecurityToken>\r\n        </ses:Session>\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/PNRADD_21_1_1A</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <PNR_AddMultiElements>\r\n            <pnrActions>\r\n                <optionCode>0</optionCode>\r\n            </pnrActions>\r\n            <travellerInfo>\r\n                <elementManagementPassenger>\r\n                    <reference>\r\n                        <qualifier>PR</qualifier>\r\n                        <number>1</number>\r\n                    </reference>\r\n                    <segmentName>NM</segmentName>\r\n                </elementManagementPassenger>\r\n                <passengerData>\r\n                    <travellerInformation>\r\n                        <traveller>\r\n                            <surname>Kottepettu</surname>\r\n                            <quantity>2</quantity>\r\n                        </traveller>\r\n                        <passenger>\r\n                            <firstName>Vishnu</firstName>\r\n                            <type>ADT</type>\r\n                            <infantIndicator>3</infantIndicator>\r\n                        </passenger>\r\n                    </travellerInformation>\r\n                    <dateOfBirth>\r\n                        <dateAndTimeDetails>\r\n                            <date>30OCT98</date>\r\n                        </dateAndTimeDetails>\r\n                    </dateOfBirth>\r\n                </passengerData>\r\n                <passengerData>\r\n                    <travellerInformation>\r\n                        <traveller>\r\n                            <surname>Kottepettu</surname>\r\n                        </traveller>\r\n                        <passenger>\r\n                            <firstName>Chloe</firstName>\r\n                            <type>INF</type>\r\n                        </passenger>\r\n                    </travellerInformation>\r\n                    <dateOfBirth>\r\n                        <dateAndTimeDetails>\r\n                            <date>03JAN24</date>\r\n                        </dateAndTimeDetails>\r\n                    </dateOfBirth>\r\n                </passengerData>\r\n            </travellerInfo>\r\n            <travellerInfo>\r\n                <elementManagementPassenger>\r\n                    <reference>\r\n                        <qualifier>PR</qualifier>\r\n                        <number>2</number>\r\n                    </reference>\r\n                    <segmentName>NM</segmentName>\r\n                </elementManagementPassenger>\r\n                <passengerData>\r\n                    <travellerInformation>\r\n                        <traveller>\r\n                            <surname>Doe</surname>\r\n                            <quantity>1</quantity>\r\n                        </traveller>\r\n                        <passenger>\r\n                            <firstName>Jane</firstName>\r\n                            <type>ADT</type>\r\n                        </passenger>\r\n                    </travellerInformation>\r\n                    <dateOfBirth>\r\n                        <dateAndTimeDetails>\r\n                            <date>27DEC01</date>\r\n                        </dateAndTimeDetails>\r\n                    </dateOfBirth>\r\n                </passengerData>\r\n            </travellerInfo>\r\n            <travellerInfo>\r\n                <elementManagementPassenger>\r\n                    <reference>\r\n                        <qualifier>PR</qualifier>\r\n                        <number>3</number>\r\n                    </reference>\r\n                    <segmentName>NM</segmentName>\r\n                </elementManagementPassenger>\r\n                <passengerData>\r\n                    <travellerInformation>\r\n                        <traveller>\r\n                            <surname>Doe</surname>\r\n                            <quantity>1</quantity>\r\n                        </traveller>\r\n                        <passenger>\r\n                            <firstName>Emily</firstName>\r\n                            <type>CHD</type>\r\n                        </passenger>\r\n                    </travellerInformation>\r\n                    <dateOfBirth>\r\n                        <dateAndTimeDetails>\r\n                            <date>01MAY18</date>\r\n                        </dateAndTimeDetails>\r\n                    </dateOfBirth>\r\n                </passengerData>\r\n            </travellerInfo>\r\n            <dataElementsMaster>\r\n                <marker1/>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <reference>\r\n                            <qualifier>OT</qualifier>\r\n                            <number>1</number>\r\n                        </reference>\r\n                        <segmentName>AP</segmentName>\r\n                    </elementManagementData>\r\n                    <freetextData>\r\n                        <freetextDetail>\r\n                            <subjectQualifier>3</subjectQualifier>\r\n                            <type>6</type>\r\n                        </freetextDetail>\r\n                        <longFreetext>+91 917012884807</longFreetext>\r\n                    </freetextData>\r\n                </dataElementsIndiv>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <reference>\r\n                            <qualifier>OT</qualifier>\r\n                            <number>2</number>\r\n                        </reference>\r\n                        <segmentName>AP</segmentName>\r\n                    </elementManagementData>\r\n                    <freetextData>\r\n                        <freetextDetail>\r\n                            <subjectQualifier>3</subjectQualifier>\r\n                            <type>P02</type>\r\n                        </freetextDetail>\r\n                        <longFreetext><EMAIL></longFreetext>\r\n                    </freetextData>\r\n                </dataElementsIndiv>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <reference>\r\n                            <qualifier>OT</qualifier>\r\n                            <number>4</number>\r\n                        </reference>\r\n                        <segmentName>SSR</segmentName>\r\n                    </elementManagementData>\r\n                    <serviceRequest>\r\n                        <ssr>\r\n                            <type>DOCS</type>\r\n                            <status>HK</status>\r\n                            <quantity>1</quantity>\r\n                            <companyId>YY</companyId>\r\n                            <freetext>----30OCT98-M--KOTTEPETTU-VISHNU</freetext>\r\n                        </ssr>\r\n                    </serviceRequest>\r\n                    <referenceForDataElement>\r\n                        <reference>\r\n                            <qualifier>PR</qualifier>\r\n                            <number>1</number>\r\n                        </reference>\r\n                    </referenceForDataElement>\r\n                </dataElementsIndiv>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <reference>\r\n                            <qualifier>OT</qualifier>\r\n                            <number>3</number>\r\n                        </reference>\r\n                        <segmentName>SSR</segmentName>\r\n                    </elementManagementData>\r\n                    <serviceRequest>\r\n                        <ssr>\r\n                            <type>DOCS</type>\r\n                            <status>HK</status>\r\n                            <quantity>1</quantity>\r\n                            <companyId>YY</companyId>\r\n                            <freetext>----27DEC01-F--Doe-Jane</freetext>\r\n                        </ssr>\r\n                    </serviceRequest>\r\n                    <referenceForDataElement>\r\n                        <reference>\r\n                            <qualifier>PR</qualifier>\r\n                            <number>2</number>\r\n                        </reference>\r\n                    </referenceForDataElement>\r\n                </dataElementsIndiv>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <reference>\r\n                            <qualifier>OT</qualifier>\r\n                            <number>3</number>\r\n                        </reference>\r\n                        <segmentName>SSR</segmentName>\r\n                    </elementManagementData>\r\n                    <serviceRequest>\r\n                        <ssr>\r\n                            <type>DOCS</type>\r\n                            <status>HK</status>\r\n                            <quantity>1</quantity>\r\n                            <companyId>YY</companyId>\r\n                            <freetext>----01MAY18-F--Doe-Emily</freetext>\r\n                        </ssr>\r\n                    </serviceRequest>\r\n                    <referenceForDataElement>\r\n                        <reference>\r\n                            <qualifier>PR</qualifier>\r\n                            <number>3</number>\r\n                        </reference>\r\n                    </referenceForDataElement>\r\n                </dataElementsIndiv>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <reference>\r\n                            <qualifier>OT</qualifier>\r\n                            <number>5</number>\r\n                        </reference>\r\n                        <segmentName>SSR</segmentName>\r\n                    </elementManagementData>\r\n                    <serviceRequest>\r\n                        <ssr>\r\n                            <type>DOCS</type>\r\n                            <status>HK</status>\r\n                            <quantity>1</quantity>\r\n                            <companyId>YY</companyId>\r\n                            <freetext>----03JAN24-FI--KOTTEPETTU-CHLOE</freetext>\r\n                        </ssr>\r\n                    </serviceRequest>\r\n                    <referenceForDataElement>\r\n                        <reference>\r\n                            <qualifier>PR</qualifier>\r\n                            <number>1</number>\r\n                        </reference>\r\n                    </referenceForDataElement>\r\n                </dataElementsIndiv>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <segmentName>SSR</segmentName>\r\n                    </elementManagementData>\r\n                    <serviceRequest>\r\n                        <ssr>\r\n                            <type>CTCE</type>\r\n                            <status>HK</status>\r\n                            <companyId>YY</companyId>\r\n                            <freetext>VISHNU//ECOGO.AI</freetext>\r\n                        </ssr>\r\n                    </serviceRequest>\r\n                </dataElementsIndiv>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <segmentName>SSR</segmentName>\r\n                    </elementManagementData>\r\n                    <serviceRequest>\r\n                        <ssr>\r\n                            <type>CTCM</type>\r\n                            <status>HK</status>\r\n                            <companyId>YY</companyId>\r\n                            <freetext>917012884807/IN</freetext>\r\n                        </ssr>\r\n                    </serviceRequest>\r\n                </dataElementsIndiv>\r\n            </dataElementsMaster>\r\n        </PNR_AddMultiElements>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}, {"name": "FOP_CreateFormOfPayment", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// For debugging: console.log('SHA1 Password:', CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Concatenate nonce (plain, not base64) + timestamp + SHA1(password)\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/TFOPCQ_19_2_1A", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <ses:Session TransactionStatusCode=\"InSeries\">\r\n            <ses:SessionId>05GL6KYWVY</ses:SessionId>\r\n            <ses:SequenceNumber>3</ses:SequenceNumber>\r\n            <ses:SecurityToken>2G1IR8IBVRUTA3O1RKJ1MHBETX</ses:SecurityToken>\r\n        </ses:Session>\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/TFOPCQ_19_2_1A</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <FOP_CreateFormOfPayment>\r\n                <transactionContext>\r\n                    <transactionDetails>\r\n                        <code>FP</code>\r\n                    </transactionDetails>\r\n                </transactionContext>\r\n                <fopGroup>\r\n                    <fopReference/>\r\n                    <mopDescription>\r\n                        <fopSequenceNumber>\r\n                            <sequenceDetails>\r\n                                <number>1</number>\r\n                            </sequenceDetails>\r\n                        </fopSequenceNumber>\r\n                        <mopDetails>\r\n                            <fopPNRDetails>\r\n                                <fopDetails>\r\n                                    <fopCode>CASH</fopCode>\r\n                                </fopDetails>\r\n                            </fopPNRDetails>\r\n                        </mopDetails>\r\n                    </mopDescription>\r\n                </fopGroup>\r\n            </FOP_CreateFormOfPayment>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}, {"name": "Fare_PricePNRWithBookingClass", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// For debugging: console.log('SHA1 Password:', CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Concatenate nonce (plain, not base64) + timestamp + SHA1(password)\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/TPCBRQ_18_1_1A", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <ses:Session TransactionStatusCode=\"InSeries\">\r\n            <ses:SessionId>05GL6KYWVY</ses:SessionId>\r\n            <ses:SequenceNumber>4</ses:SequenceNumber>\r\n            <ses:SecurityToken>2G1IR8IBVRUTA3O1RKJ1MHBETX</ses:SecurityToken>\r\n        </ses:Session>\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/TPCBRQ_18_1_1A</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <Fare_PricePNRWithBookingClass>\r\n            <pricingOptionGroup>\r\n                <pricingOptionKey>\r\n                    <pricingOptionKey>RP</pricingOptionKey>\r\n                </pricingOptionKey>\r\n            </pricingOptionGroup>\r\n            <pricingOptionGroup>\r\n                <pricingOptionKey>\r\n                    <pricingOptionKey>RU</pricingOptionKey>\r\n                </pricingOptionKey>\r\n            </pricingOptionGroup>\r\n            <pricingOptionGroup>\r\n                <pricingOptionKey>\r\n                    <pricingOptionKey>RW</pricingOptionKey>\r\n                </pricingOptionKey>\r\n                <optionDetail>\r\n                    <criteriaDetails>\r\n                        <attributeType>565376</attributeType>\r\n                    </criteriaDetails>\r\n                </optionDetail>\r\n            </pricingOptionGroup>\r\n            <pricingOptionGroup>\r\n                <pricingOptionKey>\r\n                    <pricingOptionKey>VC</pricingOptionKey>\r\n                </pricingOptionKey>\r\n                <carrierInformation>\r\n                    <companyIdentification>\r\n                        <otherCompany>AI</otherCompany>\r\n                    </companyIdentification>\r\n                </carrierInformation>\r\n            </pricingOptionGroup>\r\n        </Fare_PricePNRWithBookingClass>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}, {"name": "Ticket_CreateTSTFromPricing", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// For debugging: console.log('SHA1 Password:', CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Concatenate nonce (plain, not base64) + timestamp + SHA1(password)\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/TAUTCQ_04_1_1A", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <ses:Session TransactionStatusCode=\"InSeries\">\r\n            <ses:SessionId>05GL6KYWVY</ses:SessionId>\r\n            <ses:SequenceNumber>5</ses:SequenceNumber>\r\n            <ses:SecurityToken>2G1IR8IBVRUTA3O1RKJ1MHBETX</ses:SecurityToken>\r\n        </ses:Session>\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/TAUTCQ_04_1_1A</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <Ticket_CreateTSTFromPricing xmlns=\"http://xml.amadeus.com/TAUTCQ_04_1_1A\">\r\n            <psaList>\r\n                <itemReference>\r\n                    <referenceType>TST</referenceType>\r\n                    <uniqueReference>1</uniqueReference>\r\n                </itemReference>\r\n            </psaList>\r\n            <psaList>\r\n                <itemReference>\r\n                    <referenceType>TST</referenceType>\r\n                    <uniqueReference>2</uniqueReference>\r\n                </itemReference>\r\n            </psaList>\r\n            <psaList>\r\n                <itemReference>\r\n                    <referenceType>TST</referenceType>\r\n                    <uniqueReference>3</uniqueReference>\r\n                </itemReference>\r\n            </psaList>\r\n        </Ticket_CreateTSTFromPricing>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}, {"name": "PNR_AddMultiElements_2", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// For debugging: console.log('SHA1 Password:', CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Concatenate nonce (plain, not base64) + timestamp + SHA1(password)\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/PNRADD_21_1_1A", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <ses:Session TransactionStatusCode=\"InSeries\">\r\n            <ses:SessionId>05GL6KYWVY</ses:SessionId>\r\n            <ses:SequenceNumber>6</ses:SequenceNumber>\r\n            <ses:SecurityToken>2G1IR8IBVRUTA3O1RKJ1MHBETX</ses:SecurityToken>\r\n        </ses:Session>\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/PNRADD_21_1_1A</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <PNR_AddMultiElements>\r\n            <pnrActions>\r\n                <optionCode>11</optionCode>\r\n            </pnrActions>\r\n            <dataElementsMaster>\r\n                <marker1/>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <segmentName>TK</segmentName>\r\n                    </elementManagementData>\r\n                    <ticketElement>\r\n                        <ticket>\r\n                            <indicator>OK</indicator>\r\n                        </ticket>\r\n                    </ticketElement>\r\n                </dataElementsIndiv>\r\n                <dataElementsIndiv>\r\n                    <elementManagementData>\r\n                        <segmentName>RF</segmentName>\r\n                    </elementManagementData>\r\n                    <freetextData>\r\n                        <freetextDetail>\r\n                            <subjectQualifier>3</subjectQualifier>\r\n                            <type>P22</type>\r\n                        </freetextDetail>\r\n                        <longFreetext>TEST WBS</longFreetext>\r\n                    </freetextData>\r\n                </dataElementsIndiv>\r\n            </dataElementsMaster>\r\n        </PNR_AddMultiElements>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}, {"name": "PNR_Retrieve", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// For debugging: console.log('SHA1 Password:', CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Concatenate nonce (plain, not base64) + timestamp + SHA1(password)\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/PNRRET_21_1_1A", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <ses:Session TransactionStatusCode=\"Start\"/>\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/PNRRET_21_1_1A</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n        <oas:Security\r\n            xmlns:oas=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"\r\n            xmlns:oas1=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">\r\n            <oas:UsernameToken oas1:Id=\"UsernameToken-1\">\r\n                <oas:Username>{{username}}</oas:Username>\r\n                <oas:Nonce EncodingType=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary\">{{base64Nonce}}</oas:Nonce>\r\n                <oas:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest\">{{passwordDigest}}</oas:Password>\r\n                <oas1:Created>{{timestamp}}</oas1:Created>\r\n            </oas:UsernameToken>\r\n        </oas:Security>\r\n        <sec:AMA_SecurityHostedUser>\r\n            <sec:UserID AgentDutyCode=\"SU\" POS_Type=\"1\" RequestorType=\"U\" PseudoCityCode=\"{{officeId}}\" />\r\n        </sec:AMA_SecurityHostedUser>\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <PNR_Retrieve>\r\n            <retrievalFacts>\r\n                <retrieve>\r\n                    <type>2</type>\r\n                </retrieve>\r\n                <reservationOrProfileIdentifier>\r\n                    <reservation>\r\n                        <controlNumber>LKZMWB</controlNumber>\r\n                    </reservation>\r\n                </reservationOrProfileIdentifier>\r\n            </retrievalFacts>\r\n        </PNR_Retrieve>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}, {"name": "DocIssuance_IssueTicket", "event": [{"listen": "prerequest", "script": {"exec": ["function generateRandomString(length) {\r", "    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < length; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate MessageID in format required by Amadeus\r", "const messageId = `WbsConsu-${generateRandomString(31)}-${generateRandomString(9)}`;\r", "pm.variables.set('messageId', messageId);\r", "\r", "// Generate nonce (16 random characters)\r", "const nonce = generateRandomString(16);\r", "pm.variables.set('nonce', nonce);\r", "\r", "// Get current timestamp in ISO format with Z suffix\r", "const timestamp = new Date().toISOString();\r", "pm.variables.set('timestamp', timestamp);\r", "\r", "// Base64 encode the nonce\r", "const base64Nonce = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(nonce));\r", "pm.variables.set('base64Nonce', base64Nonce);\r", "\r", "// Get password from environment\r", "const password = pm.environment.get('password');\r", "\r", "// Calculate password digest - this is the critical part matching your Python code\r", "// First calculate SHA1 of the password\r", "const sha1Password = CryptoJS.SHA1(password);\r", "\r", "// For debugging: console.log('SHA1 Password:', CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Concatenate nonce (plain, not base64) + timestamp + SHA1(password)\r", "// Convert hex SHA1 password to binary first\r", "const sha1PasswordBinary = CryptoJS.enc.Hex.parse(CryptoJS.enc.Hex.stringify(sha1Password));\r", "\r", "// Create a WordArray of the nonce and timestamp\r", "const nonceWA = CryptoJS.enc.Utf8.parse(nonce);\r", "const timestampWA = CryptoJS.enc.Utf8.parse(timestamp);\r", "\r", "// Concatenate all three elements\r", "const combined = CryptoJS.lib.WordArray.create();\r", "combined.concat(nonceWA);\r", "combined.concat(timestampWA);\r", "combined.concat(sha1PasswordBinary);\r", "\r", "// Calculate SHA1 of the combined value\r", "const digest = CryptoJS.SHA1(combined);\r", "\r", "// Convert to Base64\r", "const passwordDigest = CryptoJS.enc.Base64.stringify(digest);\r", "pm.variables.set('passwordDigest', passwordDigest);\r", "\r", "// For debugging\r", "console.log('MessageID:', messageId);\r", "console.log('Nonce:', nonce);\r", "console.log('Base64 Nonce:', base64Nonce);\r", "console.log('Timestamp:', timestamp);\r", "console.log('Password Digest:', passwordDigest);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml;charset=UTF-8", "type": "text"}, {"key": "SOAPAction", "value": "http://webservices.amadeus.com/TTKTIQ_15_1_1A", "type": "text"}], "body": {"mode": "raw", "raw": "<soap:Envelope \r\n    xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"\r\n    xmlns:sec=\"http://xml.amadeus.com/2010/06/Security_v1\"\r\n    xmlns:typ=\"http://xml.amadeus.com/2010/06/Types_v1\"\r\n    xmlns:iat=\"http://www.iata.org/IATA/2007/00/IATA2010.1\"\r\n    xmlns:app=\"http://xml.amadeus.com/2010/06/AppMdw_CommonTypes_v3\"\r\n    xmlns:link=\"http://wsdl.amadeus.com/2010/06/ws/Link_v1\"\r\n    xmlns:ses=\"http://xml.amadeus.com/2010/06/Session_v3\">\r\n    <soap:Header xmlns:add=\"http://www.w3.org/2005/08/addressing\">\r\n        <ses:Session TransactionStatusCode=\"InSeries\">\r\n            <ses:SessionId>05GL6KYWVY</ses:SessionId>\r\n            <ses:SequenceNumber>7</ses:SequenceNumber>\r\n            <ses:SecurityToken>2G1IR8IBVRUTA3O1RKJ1MHBETX</ses:SecurityToken>\r\n        </ses:Session>\r\n        <add:MessageID>{{messageId}}</add:MessageID>\r\n        <add:Action>http://webservices.amadeus.com/TTKTIQ_15_1_1A</add:Action>\r\n        <add:To>{{endpoint}}/{{wsap}}</add:To>\r\n        <link:TransactionFlowLink />\r\n    </soap:Header>\r\n    <soap:Body>\r\n        <DocIssuance_IssueTicket>\r\n            <optionGroup>\r\n                <switches>\r\n                    <statusDetails>\r\n                        <indicator>ET</indicator>\r\n                    </statusDetails>\r\n                </switches>\r\n            </optionGroup>\r\n        </DocIssuance_IssueTicket>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{endpoint}}", "host": ["{{endpoint}}"]}}, "response": []}]}