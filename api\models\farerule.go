package models

// FareRuleRequest represents the request for fare rules with session support
type FareRuleRequest struct {
	TUI      string `json:"TUI,omitempty"`
	From     string `json:"From" validate:"required"`
	To       string `json:"To" validate:"required"`
	FlightNo string `json:"FlightNo,omitempty"`
	Provider string `json:"Provider,omitempty"`
	FUID     string `json:"FUID,omitempty"`
	FareKey  string `json:"FareKey,omitempty"`
	// Session information for stateful Amadeus calls
	SessionId      string `json:"SessionId,omitempty"`
	SequenceNumber int    `json:"SequenceNumber,omitempty"`
	SecurityToken  string `json:"SecurityToken,omitempty"`
	// Pricing reference information (mandatory for Fare_CheckRules)
	PricingRecordId  string   `json:"PricingRecordId,omitempty"`
	FareComponentIds []string `json:"FareComponentIds,omitempty"`
	// Additional fields for Amadeus Fare_CheckRules
	FareComponent  int      `json:"FareComponent,omitempty"`  // Default 1
	RuleCategories []string `json:"RuleCategories,omitempty"` // PE, MX, SR, etc.
}

// FareRuleResponse represents the exact fare rules response structure
// Matches the format: {TUI: "...", Code: "200", Msg: ["Success"], Trips: [...]}
type FareRuleResponse struct {
	TUI   string     `json:"TUI"`
	Code  string     `json:"Code"`
	Msg   []string   `json:"Msg"`
	Trips []FareTrip `json:"Trips"`
}

// FareTrip represents a trip with fare rules
type FareTrip struct {
	Journey []FareJourney `json:"Journey"`
}

// FareJourney represents journey fare rules
type FareJourney struct {
	Provider            string        `json:"Provider"`
	SpecialInformations string        `json:"SpecialInformations"`
	Segments            []FareSegment `json:"Segments"`
}

// FareSegment represents segment fare rules
type FareSegment struct {
	FUID  string     `json:"FUID"`
	VAC   string     `json:"VAC"`
	Rules []FareRule `json:"Rules"`
}

// FareRule represents individual fare rules
type FareRule struct {
	Rule []RuleDetail `json:"Rule"`
}

// RuleDetail represents detailed rule information
// Matches format: {Head: "Change Fee", Info: [...]}
type RuleDetail struct {
	Head string     `json:"Head"`
	Info []RuleInfo `json:"Info"`
}

// RuleInfo represents rule information details matching exact format
// Matches: {AdultAmount: "Not-Permitted", ChildAmount: "", InfantAmount: "", CurrencyCode: "", Description: "0 HRS - 24 HRS To Departure", RuleText: ""}
type RuleInfo struct {
	AdultAmount  string `json:"AdultAmount"`
	ChildAmount  string `json:"ChildAmount"`
	InfantAmount string `json:"InfantAmount"`
	CurrencyCode string `json:"CurrencyCode"`
	Description  string `json:"Description"`
	RuleText     string `json:"RuleText"`
}

// Predefined rule categories for Amadeus Fare_CheckRules
var FareRuleCategories = map[string]string{
	"RU": "RULE APPLICATION",
	"MN": "MIN STAY",
	"MX": "MAX STAY",
	"SE": "SEASONS",
	"SR": "SALES RESTRICT",
	"AP": "ADVANCE RES/TKT",
	"FL": "FLT APPLICATION",
	"CD": "CHILD DISCOUNTS",
	"OD": "OTHER DISCOUNTS",
	"SO": "STOPOVERS",
	"TF": "TRANSFERS/RTGS",
	"SU": "SURCHARGES",
	"PE": "PENALTIES",
	"CO": "COMBINABILITY",
	"HI": "HIGHER INTERMEDIATE POINT",
	"MD": "MISCELLANEOUS DATA",
	"VC": "VOLUNTARY CHANGES",
	"VR": "VOLUNTARY REFUNDS",
}

// GetDefaultFareRules returns the default fare rule structure
func GetDefaultFareRules() []RuleDetail {
	return []RuleDetail{
		{
			Head: "Change Fee",
			Info: []RuleInfo{
				{
					AdultAmount:  "Not-Permitted",
					ChildAmount:  "",
					InfantAmount: "",
					CurrencyCode: "",
					Description:  "0 HRS  - 24 HRS  To Departure",
					RuleText:     "",
				},
				{
					AdultAmount:  "2999",
					ChildAmount:  "",
					InfantAmount: "",
					CurrencyCode: "",
					Description:  "24 HRS  - 4 Days  To Departure",
					RuleText:     "",
				},
				{
					AdultAmount:  "2999",
					ChildAmount:  "",
					InfantAmount: "",
					CurrencyCode: "",
					Description:  "4 Days  - 999 Days  To Departure",
					RuleText:     "",
				},
			},
		},
		{
			Head: "Cancellation Fee",
			Info: []RuleInfo{
				{
					AdultAmount:  "Non refundable",
					ChildAmount:  "",
					InfantAmount: "",
					CurrencyCode: "",
					Description:  "0 HRS  - 999 Days  To Departure",
					RuleText:     "",
				},
			},
		},
		{
			Head: "ATO Service Fee",
			Info: []RuleInfo{
				{
					AdultAmount:  "0",
					ChildAmount:  "",
					InfantAmount: "",
					CurrencyCode: "",
					Description:  "Service Fee",
					RuleText:     "",
				},
			},
		},
	}
}
