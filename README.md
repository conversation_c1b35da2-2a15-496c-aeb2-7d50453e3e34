# Provider Service

A Go-based service that converts JSON flight search requests to Amadeus SOAP XML API calls and returns JSON responses.

## Overview

The Provider Service acts as a bridge between modern JSON APIs and the Amadeus Enterprise SOAP XML API. It handles:

- **JSON to XML conversion**: Converts incoming JSON requests to SOAP XML format
- **WS-Security authentication**: Implements UsernameToken authentication for Amadeus
- **XML to JSON conversion**: Converts Amadeus XML responses back to JSON
- **Multiple trip types**: Supports one-way, round-trip, and multi-city searches
- **Passenger types**: Handles adults, children, and infants with proper pricing

## Flow

```
JSON Request → Build SOAP XML → Send to Amadeus (UsernameToken WS-Security) → Receive XML → Convert to JSON → Return JSON
```

## Quick Start

### 1. Prerequisites

- Go 1.20 or later
- Valid Amadeus API credentials
- Network access to Amadeus endpoints

### 2. Setup

```bash
# Navigate to the provider-service directory
cd provider-service

# Download dependencies
go mod download

# Build the service
go build -o bin/provider cmd/provider/main.go
```

### 3. Configuration

Edit the `.env` file with your Amadeus credentials:

```env
AMADEUS_ENDPOINT=https://test.webservices.amadeus.com
AMADEUS_WSAP=FMPTBQ_23_1_1A
AMADEUS_USERNAME=<EMAIL>
AMADEUS_PASSWORD=Reshma07@
AMADEUS_OFFICE=YOUR_OFFICE_CODE  # Get this from Amadeus support
```

**Important**: Before using the Provider Service, test your Amadeus credentials:
1. Import the Postman collection: `Amadeus/Amadeus/Booking/Amadeus Booking Collection.postman_collection.json`
2. Configure environment with your credentials
3. Test the `Fare_MasterPricerTravelBoardSearch` request
4. Get your office code from Amadeus support

### 4. Run the Service

```bash
# Using the binary
./bin/provider

# Or using go run
go run cmd/provider/main.go
```

The service will start on `http://localhost:8000`

### 5. Test the Service

```bash
# Health check
curl http://localhost:8000/

# Test flight search
curl -X POST http://localhost:8000/api/search \
  -H "Content-Type: application/json" \
  -d @test/sample-requests/one-way.json
```

## API Reference

### Endpoints

- **GET /** - Health check (returns `{"status":"ok"}`)
- **POST /api/search** - Flight search

### Request Format

```json
{
  "ADT": 1,          // Adult passengers (0-9)
  "CHD": 0,          // Child passengers (0-9)
  "INF": 0,          // Infant passengers (0-9)
  "Cabin": "Y",      // Cabin class: Y=Economy, C=Business, F=First
  "Trips": [
    {
      "From": "DEL",        // Origin airport (3-letter IATA code)
      "To": "BOM",          // Destination airport (3-letter IATA code)
      "OnwardDate": "2024-12-15",  // Departure date (YYYY-MM-DD)
      "ReturnDate": "2024-12-20"   // Return date (optional, for round-trip)
    }
  ]
}
```

### Response Format

- **200 OK**: JSON converted from Amadeus XML with flight options and pricing
- **400 Bad Request**: Validation error (invalid request format)
- **500 Internal Server Error**: Configuration error (missing credentials)
- **502 Bad Gateway**: Amadeus API error (authentication, network, etc.)

## Trip Types

### One-Way Trip
```json
{
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Trips": [
    {
      "From": "DEL",
      "To": "BOM",
      "OnwardDate": "2024-12-15"
    }
  ]
}
```

### Round-Trip (add ReturnDate)
```json
{
  "ADT": 1, "CHD": 0, "INF": 0, "Cabin": "Y",
  "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-12-15", "ReturnDate": "2024-12-20"}]
}
```

### Multi-City (multiple Trips)
```json
{
  "ADT": 1, "CHD": 0, "INF": 0, "Cabin": "Y",
  "Trips": [
    {"From": "DEL", "To": "BOM", "OnwardDate": "2024-12-15"},
    {"From": "BOM", "To": "GOA", "OnwardDate": "2024-12-18"}
  ]
}
```

## Testing

### Quick Test

```bash
# Health check
curl http://localhost:8000/

# Test flight search
curl -X POST http://localhost:8000/api/search \
  -H "Content-Type: application/json" \
  -d @test/sample-requests/one-way.json
```

### Test Different Trip Types

```bash
# Round-trip (inline JSON)
curl -X POST http://localhost:8000/api/search \
  -H "Content-Type: application/json" \
  -d '{"ADT":1,"CHD":0,"INF":0,"Cabin":"Y","Trips":[{"From":"DEL","To":"BOM","OnwardDate":"2024-12-15","ReturnDate":"2024-12-20"}]}'

# Multi-city (inline JSON)
curl -X POST http://localhost:8000/api/search \
  -H "Content-Type: application/json" \
  -d '{"ADT":1,"CHD":0,"INF":0,"Cabin":"Y","Trips":[{"From":"DEL","To":"BOM","OnwardDate":"2024-12-15"},{"From":"BOM","To":"GOA","OnwardDate":"2024-12-18"}]}'
```

## Deployment

### Docker

```bash
# Build Docker image
docker build -t provider-service .

# Run with Docker Compose
docker-compose up
```

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `PORT` | No | Service port (default: 8000) |
| `AMADEUS_ENDPOINT` | Yes | Amadeus API endpoint |
| `AMADEUS_WSAP` | Yes | Web Service Access Point |
| `AMADEUS_USERNAME` | Yes | Amadeus username |
| `AMADEUS_PASSWORD` | Yes | Amadeus password |
| `AMADEUS_OFFICE` | Yes | Office/PCC code |
| `LOG_LEVEL` | No | Logging level (default: INFO) |

## Getting Your Office Code

Contact Amadeus support to get your office/PCC code:
1. Email Amadeus technical support with your account details
2. Request your Pseudo City Code (PCC) or Office ID
3. Update the `AMADEUS_OFFICE` variable in `.env`

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify Amadeus credentials
2. **Network Errors**: Check connectivity to Amadeus endpoints
3. **Invalid Requests**: Ensure proper JSON format and valid airport codes
4. **Build Errors**: Run `go mod tidy` and check Go version

### Debug Mode

Set `LOG_LEVEL=DEBUG` in `.env` for detailed logging.

## Getting Your Office Code

Contact Amadeus support to get your office/PCC code:
1. Email Amadeus technical support with your account details
2. Request your Pseudo City Code (PCC) or Office ID
3. Update the `AMADEUS_OFFICE` variable in `.env`

## Support

For issues:
1. Test Amadeus credentials with the Postman collection first
2. Check the configuration guide: `docs/CONFIGURATION.md`
3. Review sample requests in `test/sample-requests/`
4. Verify environment variables are set correctly
