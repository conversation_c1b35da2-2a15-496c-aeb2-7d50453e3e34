package models

// FlightInfoRequest represents the request for flight information
type FlightInfoRequest struct {
	TUI        string `json:"TUI,omitempty"`
	From       string `json:"From" validate:"required"`
	To         string `json:"To" validate:"required"`
	OnwardDate string `json:"OnwardDate" validate:"required"`
	ReturnDate string `json:"ReturnDate,omitempty"`
	ADT        int    `json:"ADT" validate:"min=0"`
	CHD        int    `json:"CHD" validate:"min=0"`
	INF        int    `json:"INF" validate:"min=0"`
	Cabin      string `json:"Cabin,omitempty"`
}

// FlightInfoResponse represents the exact flight information response structure
// Matches: {TUI: "...", ADT: 1, CHD: 0, CeilingInfo: "R0", Code: "200", FareRecommendations: [...], ...}
type FlightInfoResponse struct {
	TUI                 string                `json:"TUI"`
	Code                string                `json:"Code"`
	Msg                 []string              `json:"Msg"`
	ADT                 int                   `json:"ADT"`
	CHD                 int                   `json:"CHD"`
	INF                 int                   `json:"INF"`
	CeilingInfo         string                `json:"CeilingInfo"`
	FareRecommendations []FareRecommendation  `json:"FareRecommendations"`
	From                string                `json:"From"`
	To                  string                `json:"To"`
	OnwardDate          string                `json:"OnwardDate"`
	ReturnDate          string                `json:"ReturnDate"`
	GrossAmount         int                   `json:"GrossAmount"`
	NetAmount           int                   `json:"NetAmount"`
	SSRAmount           int                   `json:"SSRAmount"`
	GeneralKeys         interface{}           `json:"GeneralKeys"`
	Trips               []FlightTrip          `json:"Trips"`
}

// FareRecommendation represents fare recommendations
// Matches: {JourneyKey: "BOM-DEL,2007,6E,2025-08-19,E", Recommendations: [...]}
type FareRecommendation struct {
	JourneyKey      string              `json:"JourneyKey"`
	Recommendations []RecommendationItem `json:"Recommendations"`
}

// RecommendationItem represents individual recommendation
// Matches: {Index: "6E|20", FareKey: "INR,R,5448", NetFare: 5448, RBD: "R", FareClass: "J", FBC: "RUIP", Amenities: null}
type RecommendationItem struct {
	Index     string      `json:"Index"`
	FareKey   string      `json:"FareKey"`
	NetFare   int         `json:"NetFare"`
	RBD       string      `json:"RBD"`
	FareClass string      `json:"FareClass"`
	FBC       string      `json:"FBC"`
	Amenities interface{} `json:"Amenities"`
}

// FlightTrip represents a flight trip
type FlightTrip struct {
	Journey []FlightJourney `json:"Journey"`
}

// FlightJourney represents flight journey details
// Matches: {Provider: "6E", OrderID: 0, Stops: 0, Index: "6E|19", SPFareNotice: "", GrossFare: 5295, ...}
type FlightJourney struct {
	Provider        string         `json:"Provider"`
	OrderID         int            `json:"OrderID"`
	Stops           int            `json:"Stops"`
	Index           string         `json:"Index"`
	SPFareNotice    string         `json:"SPFareNotice"`
	GrossFare       int            `json:"GrossFare"`
	NetFare         int            `json:"NetFare"`
	FareKey         string         `json:"FareKey"`
	JourneyKey      string         `json:"JourneyKey"`
	PaxCategory     string         `json:"PaxCategory"`
	PrivateFarePCC  string         `json:"PrivateFarePCC"`
	PrivateFareType string         `json:"PrivateFareType"`
	Notices         []FlightNotice `json:"Notices"`
	Segments        []FlightSegment `json:"Segments"`
	Fares           FlightFares    `json:"Fares"`
}

// FlightNotice represents flight notices
// Matches: {Notice: "Meal, seat, Rescheduling & Cancellation are chargeable.", Link: "", NoticeType: "NoticeOnAvailability"}
type FlightNotice struct {
	Notice     string `json:"Notice"`
	Link       string `json:"Link"`
	NoticeType string `json:"NoticeType"`
}

// FlightSegment represents flight segment details
type FlightSegment struct {
	Flight FlightDetails `json:"Flight"`
	Fares  FlightFares   `json:"Fares"`
}

// FlightDetails represents detailed flight information
// Matches: {FUID: 0, VAC: "6E", MAC: "6E", OAC: "6E", FBC: "R0IP", Airline: "IndiGo|IndiGo|IndiGo", ...}
type FlightDetails struct {
	FUID              int         `json:"FUID"`
	VAC               string      `json:"VAC"`
	MAC               string      `json:"MAC"`
	OAC               string      `json:"OAC"`
	FBC               string      `json:"FBC"`
	Airline           string      `json:"Airline"`
	AirCraft          string      `json:"AirCraft"`
	ArrAirportName    string      `json:"ArrAirportName"`
	ArrivalCode       string      `json:"ArrivalCode"`
	ArrivalTerminal   string      `json:"ArrivalTerminal"`
	ArrivalTime       string      `json:"ArrivalTime"`
	Cabin             string      `json:"Cabin"`
	DepAirportName    string      `json:"DepAirportName"`
	DepartureCode     string      `json:"DepartureCode"`
	DepartureTerminal string      `json:"DepartureTerminal"`
	DepartureTime     string      `json:"DepartureTime"`
	Duration          string      `json:"Duration"`
	EmissionsInGrams  int         `json:"EmissionsInGrams"`
	EquipmentType     string      `json:"EquipmentType"`
	FCType            string      `json:"FCType"`
	FareClass         string      `json:"FareClass"`
	FlightNo          string      `json:"FlightNo"`
	Hops              interface{} `json:"Hops"`
	MACAirlineLogo    string      `json:"MACAirlineLogo"`
	OACAirlineLogo    string      `json:"OACAirlineLogo"`
	RBD               string      `json:"RBD"`
	Refundable        string      `json:"Refundable"`
	Seats             int         `json:"Seats"`
	VACAirlineLogo    string      `json:"VACAirlineLogo"`
	Amenities         interface{} `json:"Amenities"`
}

// FlightFares represents fare information
// Matches: {PTCFare: [...], GrossFare: 5295, NetFare: 5240, TotalServiceTax: 25, TotalBaseFare: 4434, ...}
type FlightFares struct {
	PTCFare                    []PTCFareDetail `json:"PTCFare"`
	GrossFare                  int             `json:"GrossFare"`
	NetFare                    float64         `json:"NetFare"`
	TotalServiceTax            int             `json:"TotalServiceTax"`
	TotalBaseFare              int             `json:"TotalBaseFare"`
	TotalCommission            float64         `json:"TotalCommission"`
	TotalMarkup                int             `json:"TotalMarkup"`
	TotalTax                   float64         `json:"TotalTax"`
	TotalAddonDiscount         int             `json:"TotalAddonDiscount"`
	TotalAddonMarkup           int             `json:"TotalAddonMarkup"`
	TotalAgentMarkUp           int             `json:"TotalAgentMarkUp"`
	TotalAtoCharge             int             `json:"TotalAtoCharge"`
	TotalReissueCharge         int             `json:"TotalReissueCharge"`
	TotalVATonServiceCharge    int             `json:"TotalVATonServiceCharge"`
	TotalVATonTransactionFee   int             `json:"TotalVATonTransactionFee"`
	DealKey                    string          `json:"DealKey"`
}

// PTCFareDetail represents passenger type code fare details
// Matches: {PTC: "ADT", Fare: 4434, YQ: 0, PSF: 0, YR: 0, UD: 0, K3: 0, API: 0, OTT: "OT,", OT: "836", Tax: 836, ...}
type PTCFareDetail struct {
	PTC                    string  `json:"PTC"`
	Fare                   int     `json:"Fare"`
	YQ                     int     `json:"YQ"`
	PSF                    int     `json:"PSF"`
	YR                     int     `json:"YR"`
	UD                     int     `json:"UD"`
	K3                     int     `json:"K3"`
	API                    int     `json:"API"`
	OTT                    string  `json:"OTT"`
	OT                     string  `json:"OT"`
	Tax                    int     `json:"Tax"`
	AddonDiscount          int     `json:"AddonDiscount"`
	AddonMarkup            int     `json:"AddonMarkup"`
	AgentMarkUp            int     `json:"AgentMarkUp"`
	AtoCharge              int     `json:"AtoCharge"`
	CMS                    float64 `json:"CMS"`
	GrossFare              int     `json:"GrossFare"`
	Markup                 int     `json:"Markup"`
	NetFare                float64 `json:"NetFare"`
	ReissueCharge          int     `json:"ReissueCharge"`
	ST                     int     `json:"ST"`
	VATonServiceCharge     int     `json:"VATonServiceCharge"`
	VATonTransactionFee    int     `json:"VATonTransactionFee"`
}
