*** First Query ***
  <Fare_CheckRules>
      <msgType>
        <messageFunctionDetails>
          <messageFunction>712</messageFunction>
        </messageFunctionDetails>
      </msgType>
      <itemNumber>
        <itemNumberDetails>
          <number>1</number>
        </itemNumberDetails>
        <itemNumberDetails>
          <number>1</number>
          <type>FC</type>
        </itemNumberDetails>
      </itemNumber>
    </Fare_CheckRules>

*** First Reply *** 
  <Fare_CheckRulesReply xmlns="http://xml.amadeus.com/FARQNR_07_1_1A">
      <transactionType>
        <messageFunctionDetails>
          <messageFunction>712</messageFunction>
        </messageFunctionDetails>
      </transactionType>
      <flightDetails>
        <nbOfSegments></nbOfSegments>
        <qualificationFareDetails>
          <fareDetails>
            <qualifier>ADT</qualifier>
            <fareCategory>ERU</fareCategory>
          </fareDetails>
          <additionalFareDetails>
            <rateClass>RL50TALB</rateClass>
            <fareClass>RL50TALB</fareClass>
          </additionalFareDetails>
          <discountDetails>
            <fareQualifier>FF</fareQualifier>
            <rateCategory>LIGHT</rateCategory>
          </discountDetails>
        </qualificationFareDetails>
        <transportService>
          <companyIdentification>
            <marketingCompany>AF</marketingCompany>
          </companyIdentification>
        </transportService>
        <flightErrorCode>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>PTC</informationType>
          </freeTextQualification>
          <freeText>ADULT</freeText>
        </flightErrorCode>
        <flightErrorCode>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>FTC</informationType>
          </freeTextQualification>
          <freeText>ECONOMY RT UNBUNDLED</freeText>
        </flightErrorCode>
        <flightErrorCode>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>FFD</informationType>
          </freeTextQualification>
          <freeText>ECONOMY LIGHT</freeText>
        </flightErrorCode>
        <productInfo>
          <productDetails>
            <bookingClassDetails>
              <designator>R</designator>
            </bookingClassDetails>
          </productDetails>
        </productInfo>
        <fareDetailInfo>
          <nbOfUnits>
            <quantityDetails>
              <numberOfUnit>1</numberOfUnit>
              <unitQualifier>ND</unitQualifier>
            </quantityDetails>
          </nbOfUnits>
          <fareDeatilInfo>
            <fareTypeGrouping>
              <pricingGroup>ADT</pricingGroup>
            </fareTypeGrouping>
          </fareDeatilInfo>
        </fareDetailInfo>
        <odiGrp>
          <originDestination>
            <origin>BOG</origin>
            <destination>FRA</destination>
          </originDestination>
        </odiGrp>
        <travellerGrp>
          <travellerIdentRef>
            <referenceDetails>
              <type>FC</type>
              <value>1</value>
            </referenceDetails>
            <referenceDetails>
              <type>RU</type>
              <value>E2LB</value>
            </referenceDetails>
          </travellerIdentRef>
          <fareRulesDetails>
            <tariffClassId>27</tariffClassId>
            <ruleSectionId>(50)</ruleSectionId>
            <ruleSectionId>(6)</ruleSectionId>
            <ruleSectionId>(7)</ruleSectionId>
            <ruleSectionId>(3)</ruleSectionId>
            <ruleSectionId>(15)</ruleSectionId>
            <ruleSectionId>(5)</ruleSectionId>
            <ruleSectionId>(4)</ruleSectionId>
            <ruleSectionId>(19)</ruleSectionId>
            <ruleSectionId>(22)</ruleSectionId>
            <ruleSectionId>(8)</ruleSectionId>
            <ruleSectionId>(9)</ruleSectionId>
            <ruleSectionId>(12)</ruleSectionId>
            <ruleSectionId>(16)</ruleSectionId>
            <ruleSectionId>(10)</ruleSectionId>
            <ruleSectionId>(17)</ruleSectionId>
            <ruleSectionId>(23)</ruleSectionId>
            <ruleSectionId>(31)</ruleSectionId>
            <ruleSectionId>(33)</ruleSectionId>
          </fareRulesDetails>
        </travellerGrp>
        <travellerGrp>
          <travellerIdentRef>
            <referenceDetails>
              <type>RU</type>
              <value>E2LB</value>
            </referenceDetails>
          </travellerIdentRef>
          <fareRulesDetails>
            <ruleSectionId>RU</ruleSectionId>
            <ruleSectionId>MN</ruleSectionId>
            <ruleSectionId>MX</ruleSectionId>
            <ruleSectionId>SE</ruleSectionId>
            <ruleSectionId>SR</ruleSectionId>
            <ruleSectionId>AP</ruleSectionId>
            <ruleSectionId>FL</ruleSectionId>
            <ruleSectionId>CD</ruleSectionId>
            <ruleSectionId>OD</ruleSectionId>
            <ruleSectionId>SO</ruleSectionId>
            <ruleSectionId>TF</ruleSectionId>
            <ruleSectionId>SU</ruleSectionId>
            <ruleSectionId>PE</ruleSectionId>
            <ruleSectionId>CO</ruleSectionId>
            <ruleSectionId>HI</ruleSectionId>
            <ruleSectionId>MD</ruleSectionId>
            <ruleSectionId>VC</ruleSectionId>
            <ruleSectionId>VR</ruleSectionId>
          </fareRulesDetails>
        </travellerGrp>
        <itemGrp>
          <itemNb>
            <itemNumberDetails>
              <number>1</number>
            </itemNumberDetails>
          </itemNb>
          <unitGrp>
            <nbOfUnits>
              <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>PR</unitQualifier>
              </quantityDetails>
              <quantityDetails>
                <numberOfUnit>2</numberOfUnit>
                <unitQualifier>PR</unitQualifier>
              </quantityDetails>
            </nbOfUnits>
            <unitFareDetails>
              <fareTypeGrouping>
                <pricingGroup>ADT</pricingGroup>
              </fareTypeGrouping>
            </unitFareDetails>
          </unitGrp>
        </itemGrp>
      </flightDetails>
    </Fare_CheckRulesReply>

*** Second Query (Specific categories) ***
 <Fare_CheckRules>
      <msgType>
        <messageFunctionDetails>
          <messageFunction>712</messageFunction>
        </messageFunctionDetails>
      </msgType>
      <itemNumber>
        <itemNumberDetails>
          <number>1</number>
        </itemNumberDetails>
        <itemNumberDetails>
          <number>1</number>
          <type>FC</type>
        </itemNumberDetails>
      </itemNumber>
      <fareRule>
        <tarifFareRule>
          <ruleSectionId>PE</ruleSectionId>
          <ruleSectionId>MX</ruleSectionId>
          <ruleSectionId>SR</ruleSectionId>
          <ruleSectionId>TR</ruleSectionId>
          <ruleSectionId>AP</ruleSectionId>
          <ruleSectionId>FL</ruleSectionId>
        </tarifFareRule>
      </fareRule>
    </Fare_CheckRules>

*** Second Reply ***
  <Fare_CheckRulesReply xmlns="http://xml.amadeus.com/FARQNR_07_1_1A">
      <transactionType>
        <messageFunctionDetails>
          <messageFunction>712</messageFunction>
        </messageFunctionDetails>
      </transactionType>
      <tariffInfo>
        <fareRuleInfo>
          <ruleSectionLocalId>1</ruleSectionLocalId>
          <ruleCategoryCode>(7)</ruleCategoryCode>
        </fareRuleInfo>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>CAT</informationType>
          </freeTextQualification>
          <freeText>MX.MAX STAY</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  TRAVEL FROM LAST STOPOVER MUST COMMENCE NO LATER THAN 12</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  MONTHS AFTER DEPARTURE FROM FARE ORIGIN.</freeText>
        </fareRuleText>
      </tariffInfo>
      <tariffInfo>
        <fareRuleInfo>
          <ruleSectionLocalId>2</ruleSectionLocalId>
          <ruleCategoryCode>(15)</ruleCategoryCode>
        </fareRuleInfo>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>CAT</informationType>
          </freeTextQualification>
          <freeText>SR.SALES RESTRICT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  TICKETS MUST BE ISSUED ON AF  AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON KL AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON AH AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON AM AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON AT AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON A9 AND MAY ONLY BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       GEORGIA</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON CM AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON CZ AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON DL AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON JU AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON KQ AND MAY ONLY BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       BURUNDI/ETHIOPIA/MALAWI/MOZAMBIQUE/RWANDA/SUDAN/</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       SOMALIA/SOUTH SUDAN/ZAMBIA/ZIMBABWE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON ME AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON MF AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON MK AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON MU AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON SB AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON SV AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON TU AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  OR - TICKETS MUST BE ISSUED ON WF AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>       VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  IF THE FARE COMPONENT IS NOT ON</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      ONE OR MORE OF THE FOLLOWING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY AF FLIGHT OPERATED BY UX</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY KL FLIGHT OPERATED BY UX</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY UX FLIGHT OPERATED BY UX</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY AF FLIGHT OPERATED BY 6E</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY KL FLIGHT OPERATED BY 6E</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 6E FLIGHT OPERATED BY 6E</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>    TICKETS MUST BE ISSUED ON VS AND MAY NOT BE SOLD IN</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>    VENEZUELA/NIGERIA/EGYPT</freeText>
        </fareRuleText>
      </tariffInfo>
      <tariffInfo>
        <fareRuleInfo>
          <ruleSectionLocalId>3</ruleSectionLocalId>
          <ruleCategoryCode>(5)</ruleCategoryCode>
        </fareRuleInfo>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>CAT</informationType>
          </freeTextQualification>
          <freeText>AP.ADVANCE RES/TKT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>FOR RL50TALB TYPE FARES</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  RESERVATIONS ARE REQUIRED FOR ALL SECTORS.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>         NOTE -</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          NOTE - DIFFERENCE COULD EXIST BETWEEN THE CRS LAST</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          TICKETING DATE AND TTL ROBOT REMARK.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          --- THE MOST RESTRICTIVE DATE PREVAILS ---</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          --------------------------------------------------</freeText>
        </fareRuleText>
      </tariffInfo>
      <tariffInfo>
        <fareRuleInfo>
          <ruleSectionLocalId>4</ruleSectionLocalId>
          <ruleCategoryCode>(4)</ruleCategoryCode>
        </fareRuleInfo>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>CAT</informationType>
          </freeTextQualification>
          <freeText>FL.FLT APPLICATION</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>FROM/TO AREA 1</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  THE FARE COMPONENT MUST NOT BE ON</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      ONE OR MORE OF THE FOLLOWING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY AF FLIGHT OPERATED BY TO</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 9K FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY AA FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY BA FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY CU FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY EK FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY FC FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY GU FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY IB FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY IG FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY KP FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY LH FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY LX FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY LY FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY QR FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY QS FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY S2 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY SK FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY SN FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY SS FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY TA FLIGHT OPERATED BY H2</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY TX FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY UA FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY US FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY VS FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY VY FLIGHT.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  THE FARE COMPONENT MUST NOT BE ON</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      ONE OR MORE OF THE FOLLOWING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 2S FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 3Y FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 4Q FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 6A FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 7P FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 8F FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY AB FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY AW FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY C2 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY D3 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY DC FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY EO FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY FG FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY FT FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY I7 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY I8 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY KF FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY KJ FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY M9 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY MQ FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY MZ FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY NV FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY O3 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY OD FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY Q4 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY Q8 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY QH FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY QZ FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY R8 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY RI FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY RQ FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY RW FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY SD FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY SM FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY SX FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY UF FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY UQ FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY W5 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY W7 FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY X7 FLIGHT.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>         NOTE -</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          THE FARE COMPONENT MUST NOT BE ON</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>              ONE OR MORE OF THE FOLLOWING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                ANY 0K FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                ANY DZ FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                ANY IP FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  THE FARE COMPONENT MUST NOT BE ON</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      ONE OR MORE OF THE FOLLOWING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY 9W FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY BE FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY CO FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY IT FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY JK FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY MA FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY PU FLIGHT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY FB FLIGHT.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  IF THE FARE COMPONENT INCLUDES TRAVEL VIA DXB</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      THEN THAT TRAVEL MUST NOT BE ON</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      ONE OR MORE OF THE FOLLOWING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY IR FLIGHT.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  IF THE FARE COMPONENT INCLUDES TRAVEL BETWEEN PAR AND BJS</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      THEN THAT TRAVEL MUST NOT BE ON</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      ONE OR MORE OF THE FOLLOWING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY AF FLIGHT OPERATED BY MU</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>        ANY MU FLIGHT OPERATED BY MU.</freeText>
        </fareRuleText>
      </tariffInfo>
      <tariffInfo>
        <fareRuleInfo>
          <ruleSectionLocalId>5</ruleSectionLocalId>
          <ruleCategoryCode>(16)</ruleCategoryCode>
        </fareRuleInfo>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>CAT</informationType>
          </freeTextQualification>
          <freeText>PE.PENALTIES</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  CHANGES</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>    BEFORE DEPARTURE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      CHARGE USD 200.00.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      CHILD/INFANT DISCOUNTS APPLY.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>         NOTE -</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          A CHANGE IS A ROUTING / DATE / FLIGHT MODIFICATION</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          WHEN MORE THAN ONE FARE COMPONENT IS BEING CHANGED</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          THE HIGHEST PENALTY OF ALL CHANGED FARE COMPONENTS</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          WILL APPLY</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                                ////</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                    // BEFORE OUTBOUND DEPARTURE //</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                                ////</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          NEW RESERVATION AND REISSUANCE MUST BE MADE AT THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          SAME TIME PRIOR TO DEPARTURE OF THE ORIGINALLY</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          SCHEDULED FLIGHT. IF CHANGE DOES NOT OCCUR ON THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          FIRST FARE COMPONENT OF THE JOURNEY NEW FARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          WILL BE RECALCULATED USING FARES IN EFFECT ON THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          PREVIOUS TICKETING DATE AND UNDER FOLLOWING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          CONDITIONS</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>           - IF SAME BOOKING CLASS IS USED NEW FARE MAY BE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             LOWER OR EQUAL OR HIGHER THAN PREVIOUS AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             A / MUST COMPLY WITH ALL PROVISIONS OF THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 ORIGINALLY TICKETED FARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             B / OR MUST COMPLY WITH ALL PROVISIONS OF THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 NEW FARE BEING APPLIED</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>           - IF A DIFFERENT BOOKING CLASS IS USED NEW FARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             MAY BE EQUAL OR HIGHER THAN PREVIOUS AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             A / MUST COMPY WITH ALL PROVISIONS OF THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 NEW FARE BEING APPLIED</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                        -----------------------</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          REISSUE IS PERMITTED WITH ANY BRAND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                        -----------------------</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          NEW RESERVATION AND REISSUANCE MUST BE MADE AT THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          SAME TIME PRIOR TO DEPARTURE OF THE ORIGINALLY</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          SCHEDULED FLIGHT. WHEN CHANGE OCCURS ON THE FIRST</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          FARE COMPONENT OF THE JOURNEY ONLY OR ON THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          FIRST FARE COMPONENT AND OTHER FARE COMPONENT OF</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          THE JOURNEY NEW FARE WILL BE RECALCULATED USING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          FARES IN EFFECT ON DATE OF REISSUE AND UNDER</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          FOLLOWING CONDITIONS</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>           - IF SAME BOOKING CLASS IS USED NEW FARE MAY BE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             LOWER OR EQUAL OR HIGHER THAN PREVIOUS AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             A / MUST COMPLY WITH ALL PROVISIONS OF THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 ORIGINALLY TICKETED FARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             B / OR MUST COMPLY WITH ALL PROVISIONS OF THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 NEW FARE BEING APPLIED</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>           - IF A DIFFERENT BOOKING CLASS IS USED NEW FARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             MAY BE EQUAL OR HIGHER THAN PREVIOUS AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             A / MUST COMPLY WITH ALL PROVISIONS OF THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 NEW FARE BEING APPLIED</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                        -----------------------</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          REISSUE IS PERMITTED WITH ANY BRAND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                        -----------------------</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>    CHANGES NOT PERMITTED IN CASE OF NO-SHOW.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>         NOTE -</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 //  BEFORE OUTBOUND DEPARTURE  //</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                          //  NO SHOW  //</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          IN THE EVENT OF NO SHOW - WHEN CHANGES ARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          REQUESTED AFTER DEPARTURE OF THE ORIGINALLY</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          SCHEDULED FLIGHT -  CHANGES ARE NOT PERMITTED AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          CANCELLATION RULES SHALL APPLY</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>    AFTER DEPARTURE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      CHARGE USD 200.00.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      CHILD/INFANT DISCOUNTS APPLY.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>         NOTE -</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          /////</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                    // AFTER OUTBOUND DEPARTURE //</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                                ////</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          NEW RESERVATION / REISSUANCE AND PAYMENT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          OF THE PENALTY MUST BE MADE AT THE SAME TIME</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                    -------------------------------</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>            NEW FARE WILL BE RECALCULATED USING</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          FARES IN EFFECT ON THE PREVIOUS TICKETING DATE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          AND UNDER FOLLOWING CONDITIONS</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>           - IF SAME BOOKING CLASS IS USED NEW FARE MAY BE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             LOWER OR EQUAL OR HIGHER THAN PREVIOUS AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             A / MUST COMPLY WITH ALL PROVISIONS OF THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 ORIGINALLY TICKETED FARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             B / OR MUST COMPLY WITH ALL PROVISIONS OF THE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                 NEW FARE BEING APPLIED</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>           - IF A DIFFERENT BOOKING CLASS IS USED NEW FARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             MAY BE EQUAL OR HIGHER THAN PREVIOUS AND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          UNDER FOLLOWING CONDITIONS</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          A / MUST COMPLY WITH ALL PROVISIONS OF THE NEW</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>              FARE BEING APPLIED</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                        -----------------------</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          REISSUE IS PERMITTED WITH ANY BRAND</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>                        -----------------------</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>  CANCELLATIONS</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>    ANY TIME</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      TICKET IS NON-REFUNDABLE IN CASE OF CANCEL.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>      TICKET IS NON-REFUNDABLE IN CASE OF NO-SHOW.</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText></freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>         NOTE -</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          ANY TIME</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          CANCELLATIONS RULES APPLY BY FARE COMPONENT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          WHEN COMBINING A REFUNDABLE TICKET WITH A</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          NON REFUNDABLE TICKET PROVISIONS WILL APPLY</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          AS FOLLOWS</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             - THE AMOUNT PAID ON THE REFUNDABLE FARE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>               COMPONENT WILL BE REFUNDED UPON PAYMENT</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>               OF THE PENALTY AMOUNT IF APPLICABLE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>             - THE AMOUNT PAID ON THE NON REFUNDABLE</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>               FARE COMPONENT WILL NOT BE REFUNDED</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          - FOR NON REFUNDABLE TICKETS THE YQ/YR CARRIER</freeText>
        </fareRuleText>
        <fareRuleText>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
          </freeTextQualification>
          <freeText>          IMPOSED SURCHARGE WILL NOT BE REFUNDED</freeText>
        </fareRuleText>
      </tariffInfo>
      <flightDetails>
        <nbOfSegments></nbOfSegments>
        <qualificationFareDetails>
          <fareDetails>
            <qualifier>ADT</qualifier>
            <fareCategory>ERU</fareCategory>
          </fareDetails>
          <additionalFareDetails>
            <rateClass>RL50TALB</rateClass>
            <fareClass>RL50TALB</fareClass>
          </additionalFareDetails>
          <discountDetails>
            <fareQualifier>FF</fareQualifier>
            <rateCategory>LIGHT</rateCategory>
          </discountDetails>
        </qualificationFareDetails>
        <transportService>
          <companyIdentification>
            <marketingCompany>AF</marketingCompany>
          </companyIdentification>
        </transportService>
        <flightErrorCode>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>PTC</informationType>
          </freeTextQualification>
          <freeText>ADULT</freeText>
        </flightErrorCode>
        <flightErrorCode>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>FTC</informationType>
          </freeTextQualification>
          <freeText>ECONOMY RT UNBUNDLED</freeText>
        </flightErrorCode>
        <flightErrorCode>
          <freeTextQualification>
            <textSubjectQualifier>3</textSubjectQualifier>
            <informationType>FFD</informationType>
          </freeTextQualification>
          <freeText>ECONOMY LIGHT</freeText>
        </flightErrorCode>
        <productInfo>
          <productDetails>
            <bookingClassDetails>
              <designator>R</designator>
            </bookingClassDetails>
          </productDetails>
        </productInfo>
        <fareDetailInfo>
          <nbOfUnits>
            <quantityDetails>
              <numberOfUnit>1</numberOfUnit>
              <unitQualifier>ND</unitQualifier>
            </quantityDetails>
          </nbOfUnits>
          <fareDeatilInfo>
            <fareTypeGrouping>
              <pricingGroup>ADT</pricingGroup>
            </fareTypeGrouping>
          </fareDeatilInfo>
        </fareDetailInfo>
        <odiGrp>
          <originDestination>
            <origin>BOG</origin>
            <destination>FRA</destination>
          </originDestination>
        </odiGrp>
        <travellerGrp>
          <travellerIdentRef>
            <referenceDetails>
              <type>FC</type>
              <value>1</value>
            </referenceDetails>
            <referenceDetails>
              <type>RU</type>
              <value>E2LB</value>
            </referenceDetails>
          </travellerIdentRef>
          <fareRulesDetails>
            <tariffClassId>27</tariffClassId>
            <ruleSectionId>1</ruleSectionId>
            <ruleSectionId>2</ruleSectionId>
            <ruleSectionId>3</ruleSectionId>
            <ruleSectionId>4</ruleSectionId>
            <ruleSectionId>5</ruleSectionId>
          </fareRulesDetails>
        </travellerGrp>
        <itemGrp>
          <itemNb>
            <itemNumberDetails>
              <number>1</number>
            </itemNumberDetails>
          </itemNb>
          <unitGrp>
            <nbOfUnits>
              <quantityDetails>
                <numberOfUnit>1</numberOfUnit>
                <unitQualifier>PR</unitQualifier>
              </quantityDetails>
              <quantityDetails>
                <numberOfUnit>2</numberOfUnit>
                <unitQualifier>PR</unitQualifier>
              </quantityDetails>
            </nbOfUnits>
            <unitFareDetails>
              <fareTypeGrouping>
                <pricingGroup>ADT</pricingGroup>
              </fareTypeGrouping>
            </unitFareDetails>
          </unitGrp>
        </itemGrp>
      </flightDetails>
    </Fare_CheckRulesReply>