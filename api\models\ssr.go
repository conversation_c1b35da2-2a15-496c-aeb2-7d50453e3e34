package models

// SSRRequest represents the request for Special Service Requests
type SSRRequest struct {
	TUI      string `json:"TUI,omitempty"`
	From     string `json:"From" validate:"required"`
	To       string `json:"To" validate:"required"`
	FlightNo string `json:"FlightNo,omitempty"`
	Provider string `json:"Provider,omitempty"`
	FUID     string `json:"FUID,omitempty"`
	PTC      string `json:"PTC,omitempty"` // Passenger Type Code (ADT/CHD/INF)
}

// SSRResponse represents the exact SSR response structure
// Matches: {TUI: "...", Code: "200", Msg: ["Success"], PaidSSR: false, Trips: [...]}
type SSRResponse struct {
	TUI     string    `json:"TUI"`
	Code    string    `json:"Code"`
	Msg     []string  `json:"Msg"`
	PaidSSR bool      `json:"PaidSSR"`
	Trips   []SSRTrip `json:"Trips"`
}

// SSRTrip represents a trip with SSR information
// Matches: {From: "BOM", To: "DEL", Journey: [...]}
type SSRTrip struct {
	From    string       `json:"From"`
	To      string       `json:"To"`
	Journey []SSRJourney `json:"Journey"`
}

// SSRJourney represents journey SSR details
// Matches: {Provider: "6E", MultiSSR: "", ConversationID: "", Segments: [...]}
type SSRJourney struct {
	Provider         string       `json:"Provider"`
	MultiSSR         string       `json:"MultiSSR"`
	ConversationID   string       `json:"ConversationID"`
	Segments         []SSRSegment `json:"Segments"`
}

// SSRSegment represents segment SSR information
// Matches: {FUID: "0", VAC: "6E", Index: null, SSR: [...]}
type SSRSegment struct {
	FUID  string    `json:"FUID"`
	VAC   string    `json:"VAC"`
	Index interface{} `json:"Index"`
	SSR   []SSRItem `json:"SSR"`
}

// SSRItem represents individual SSR service
// Matches exact format: {Code: "BAG", Description: "15Kg, 7Kg", PieceDescription: "", Charge: 0, OrginalCharge: 0, VAT: 0, ...}
type SSRItem struct {
	Code              string        `json:"Code"`
	Description       string        `json:"Description"`
	PieceDescription  string        `json:"PieceDescription"`
	Charge            int           `json:"Charge"`
	OrginalCharge     int           `json:"OrginalCharge"`
	OriginCharge      int           `json:"OriginCharge"`
	VAT               int           `json:"VAT"`
	Category          string        `json:"Category"`
	ID                int           `json:"ID"`
	OriginID          int           `json:"OriginID"`
	IsFreeMeal        bool          `json:"IsFreeMeal"`
	MealImage         string        `json:"MealImage"`
	PTC               string        `json:"PTC"`
	SSRUrl            interface{}   `json:"SSRUrl"`
	Type              string        `json:"Type"`
	VAC               string        `json:"VAC"`
	AdditionalField   []interface{} `json:"AdditionalField"`
}

// Common SSR types and codes for Amadeus
const (
	// Baggage SSR codes
	SSR_BAGGAGE = "BAG"
	SSR_EXCESS_BAG = "XBAG"
	
	// Meal SSR codes
	SSR_MEAL_VEGETARIAN = "VGML"
	SSR_MEAL_HINDU = "HNML"
	SSR_MEAL_MUSLIM = "MOML"
	SSR_MEAL_JAIN = "VJML"
	SSR_MEAL_ASIAN = "AVML"
	SSR_MEAL_KOSHER = "KSML"
	
	// Seat SSR codes
	SSR_SEAT_PREFERENCE = "SEAT"
	SSR_SEAT_WINDOW = "WCHC"
	SSR_SEAT_AISLE = "WCHR"
	
	// Special assistance
	SSR_WHEELCHAIR = "WCHR"
	SSR_UNACCOMPANIED_MINOR = "UMNR"
	SSR_INFANT = "INFT"
	
	// Documentation
	SSR_DOCS = "DOCS"
	SSR_APIS = "APIS"
)

// SSRCategory represents different categories of SSR
type SSRCategory struct {
	Category    string    `json:"category"`
	Description string    `json:"description"`
	Items       []SSRItem `json:"items"`
}

// GetDefaultSSRItems returns default SSR items matching the exact response format
func GetDefaultSSRItems(ptc string) []SSRItem {
	return []SSRItem{
		{
			Code:              "BAG",
			Description:       "15Kg, 7Kg",
			PieceDescription:  "",
			Charge:            0,
			OrginalCharge:     0,
			OriginCharge:      0,
			VAT:               0,
			Category:          "",
			ID:                1,
			OriginID:          0,
			IsFreeMeal:        false,
			MealImage:         "",
			PTC:               ptc,
			SSRUrl:            nil,
			Type:              "2",
			VAC:               "6E",
			AdditionalField:   []interface{}{},
		},
		{
			Code:              "VGML",
			Description:       "Vegetarian Meal",
			PieceDescription:  "",
			Charge:            0,
			OrginalCharge:     0,
			OriginCharge:      0,
			VAT:               0,
			Category:          "MEAL",
			ID:                2,
			OriginID:          0,
			IsFreeMeal:        true,
			MealImage:         "",
			PTC:               ptc,
			SSRUrl:            nil,
			Type:              "1",
			VAC:               "6E",
			AdditionalField:   []interface{}{},
		},
		{
			Code:              "SEAT",
			Description:       "Seat Selection",
			PieceDescription:  "",
			Charge:            500,
			OrginalCharge:     500,
			OriginCharge:      500,
			VAT:               0,
			Category:          "SEAT",
			ID:                3,
			OriginID:          0,
			IsFreeMeal:        false,
			MealImage:         "",
			PTC:               ptc,
			SSRUrl:            nil,
			Type:              "3",
			VAC:               "6E",
			AdditionalField:   []interface{}{},
		},
	}
}

// GetSSRCategories returns predefined SSR categories
func GetSSRCategories() []SSRCategory {
	return []SSRCategory{
		{
			Category:    "BAGGAGE",
			Description: "Baggage Services",
			Items: []SSRItem{
				{
					Code:        "BAG",
					Description: "15Kg, 7Kg",
					Type:        "2",
					Category:    "BAGGAGE",
				},
				{
					Code:        "XBAG",
					Description: "Extra Baggage",
					Type:        "2",
					Category:    "BAGGAGE",
					Charge:      1500,
				},
			},
		},
		{
			Category:    "MEALS",
			Description: "Meal Preferences",
			Items: []SSRItem{
				{
					Code:        "VGML",
					Description: "Vegetarian Meal",
					Type:        "1",
					Category:    "MEALS",
					IsFreeMeal:  true,
				},
				{
					Code:        "HNML",
					Description: "Hindu Meal",
					Type:        "1",
					Category:    "MEALS",
					IsFreeMeal:  true,
				},
				{
					Code:        "MOML",
					Description: "Muslim Meal",
					Type:        "1",
					Category:    "MEALS",
					IsFreeMeal:  true,
				},
			},
		},
		{
			Category:    "SEATS",
			Description: "Seat Preferences",
			Items: []SSRItem{
				{
					Code:        "SEAT",
					Description: "Seat Selection",
					Type:        "3",
					Category:    "SEATS",
					Charge:      500,
				},
			},
		},
		{
			Category:    "ASSISTANCE",
			Description: "Special Assistance",
			Items: []SSRItem{
				{
					Code:        "WCHR",
					Description: "Wheelchair",
					Type:        "4",
					Category:    "ASSISTANCE",
				},
				{
					Code:        "UMNR",
					Description: "Unaccompanied Minor",
					Type:        "4",
					Category:    "ASSISTANCE",
					Charge:      2000,
				},
			},
		},
	}
}
