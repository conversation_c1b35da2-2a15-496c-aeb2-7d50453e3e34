<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:add="http://www.w3.org/2005/08/addressing">
	<soapenv:Header>
		<add:MessageID>urn:uuid:c37b1a3d-9ec5-f226-e66b-497cb8da1178</add:MessageID>
		<add:Action>http://webservices.amadeus.com/TIPNRQ_18_1_1A</add:Action>
		<add:To>https://noded5.test.webservices.amadeus.com/1ASIWTBSB2T</add:To>
		<add:ReplyTo>
			<add:Address>http://www.w3.org/2005/08/addressing/anonymous</add:Address>
		</add:ReplyTo>
		
		<wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" soapenv:mustUnderstand="1">
			<wsse:UsernameToken wsu:Id="UsernameToken-1">
				<wsse:Username>WSB2TTBS</wsse:Username>
				<wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">geY85Iufp3KQYR27k1zRZIzRKUQ=</wsse:Password>
				<wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">x+0krJr2RL9RbuUc3rYBSw==</wsse:Nonce>
				<wsu:Created>2025-09-09T05:47:04Z</wsu:Created>
			</wsse:UsernameToken>
		</wsse:Security>
		
		<AMA_SecurityHostedUser xmlns="http://xml.amadeus.com/2010/06/Security_v1">
			<UserID AgentDutyCode="SU" RequestorType="U" PseudoCityCode="DELVS38BX" POS_Type="1" />
		</AMA_SecurityHostedUser>
	</soapenv:Header>
	<soapenv:Body>
		
<Fare_InformativePricingWithoutPNR xmlns="http://xml.amadeus.com/TIPNRQ_18_1_1A">

	<passengersGroup>
		<segmentRepetitionControl>
			<segmentControlDetails>
				<quantity>1</quantity>
				<numberOfUnits>1</numberOfUnits>
			</segmentControlDetails>
		</segmentRepetitionControl>
		<travellersID>
			<travellerDetails>
				<measurementValue>1</measurementValue>
			</travellerDetails>
		</travellersID>
		<discountPtc>
			<valueQualifier>ADT</valueQualifier>
		</discountPtc>
	</passengersGroup>

	<segmentGroup>
		<segmentInformation>
			<flightDate>
				<departureDate>191225</departureDate>
			</flightDate>
			<boardPointDetails>
				<trueLocationId>DEL</trueLocationId>
			</boardPointDetails>
			<offpointDetails>
				<trueLocationId>MAA</trueLocationId>
			</offpointDetails>
			<companyDetails>
				<marketingCompany>AI</marketingCompany>
			</companyDetails>
			<flightIdentification>
				<flightNumber>439</flightNumber>
				<bookingClass>M</bookingClass>
			</flightIdentification>
			<flightTypeDetails>
				<flightIndicator>1</flightIndicator>
			</flightTypeDetails>
			<itemNumber>1</itemNumber>
		</segmentInformation>
	</segmentGroup>
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>FCO</pricingOptionKey>
		</pricingOptionKey>
		<currency>
			<firstCurrencyDetails>
				<currencyQualifier>FCO</currencyQualifier>
				<currencyIsoCode>INR</currencyIsoCode>
			</firstCurrencyDetails>
		</currency>
	</pricingOptionGroup>
	<pricingOptionGroup>
		<pricingOptionKey>
			<pricingOptionKey>RLO</pricingOptionKey>
		</pricingOptionKey>
	</pricingOptionGroup>
</Fare_InformativePricingWithoutPNR>
	</soapenv:Body>
</soapenv:Envelope>