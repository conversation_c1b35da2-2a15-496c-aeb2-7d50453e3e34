# PowerShell script to test FareRule API with proper workflow

Write-Host "Testing FareRule API - Complete Workflow" -ForegroundColor Green

# Step 1: Call FlightInfo API to establish pricing context
Write-Host "`nStep 1: Calling FlightInfo API to get pricing context..." -ForegroundColor Yellow

$flightInfoRequest = @{
    TUI = "test-farerule-$(Get-Date -Format 'yyyyMMddHHmmss')"
    Provider = "AI"
    Currency = "INR"
    From = "DEL"
    To = "MAA"
    OnwardDate = "2025-12-19"
    ReturnDate = ""
    ADT = 1
    CHD = 0
    INF = 0
    Cabin = "ECONOMY"
    FlightNo = "439"
    Airline = "AI"
    BookingClass = "M"
    FareBasisCode = "MU1YXSII"
} | ConvertTo-Json

Write-Host "FlightInfo Request:" -ForegroundColor Cyan
Write-Host $flightInfoRequest

try {
    $flightInfoResponse = Invoke-RestMethod -Uri "http://localhost:8001/api/flightinfo" -Method POST -ContentType "application/json" -Body $flightInfoRequest
    
    Write-Host "`nFlightInfo API Response:" -ForegroundColor Green
    Write-Host ($flightInfoResponse | ConvertTo-Json -Depth 10)

    # Extract session information (if available in response)
    $tui = $flightInfoResponse.TUI
    Write-Host "`nExtracted TUI: $tui" -ForegroundColor Cyan

    # Step 2: Call FareRule API using the same flight details
    Write-Host "`nStep 2: Calling FareRule API with flight context..." -ForegroundColor Yellow
    
    $fareRuleRequest = @{
        TUI = $tui
        From = "DEL"
        To = "MAA"
        Provider = "AI"
        FUID = "1"
        FareComponent = 1
        RuleCategories = @("PE", "MX", "SR", "VC", "VR")
        FlightNo = "439"
        FareKey = "INR,T,14883"
    } | ConvertTo-Json
    
    Write-Host "FareRule Request:" -ForegroundColor Cyan
    Write-Host $fareRuleRequest
    
    $fareRuleResponse = Invoke-RestMethod -Uri "http://localhost:8001/api/farerule" -Method POST -ContentType "application/json" -Body $fareRuleRequest
    
    Write-Host "`nFareRule API Response:" -ForegroundColor Green
    Write-Host ($fareRuleResponse | ConvertTo-Json -Depth 10)
    
} catch {
    Write-Host "`nError occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`nTest completed!" -ForegroundColor Green
