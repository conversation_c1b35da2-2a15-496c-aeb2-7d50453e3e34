{"info": {"name": "FlightInfo API Tests", "description": "Complete test collection for FlightInfo API using Amadeus TIPNRQ", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8001"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}, "response": []}, {"name": "FlightInfo - Basic One Way", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"From\": \"BOM\",\n  \"To\": \"DEL\",\n  \"OnwardDate\": \"2025-08-21\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"Y\"\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}, {"name": "FlightInfo - With TUI", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"TUI\": \"ON90decf5e-9874-4a5a-bfd5-ed04fbbf8136|e091c216-6a3a-4ad5-be8a-b7d5cd544431|20250820154341\",\n  \"From\": \"BOM\",\n  \"To\": \"DEL\",\n  \"OnwardDate\": \"2025-08-21\",\n  \"ReturnDate\": \"\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"Y\",\n  \"FlightNo\": \"2986\",\n  \"Provider\": \"AI\"\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}, {"name": "FlightInfo - Round Trip", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"From\": \"BOM\",\n  \"To\": \"DEL\",\n  \"OnwardDate\": \"2025-08-21\",\n  \"ReturnDate\": \"2025-08-25\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"Y\"\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}, {"name": "FlightInfo - Multiple Passengers", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"From\": \"BOM\",\n  \"To\": \"DEL\",\n  \"OnwardDate\": \"2025-08-21\",\n  \"ADT\": 2,\n  \"CHD\": 1,\n  \"INF\": 1,\n  \"Cabin\": \"Y\"\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}, {"name": "FlightInfo - Business Class", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"From\": \"BOM\",\n  \"To\": \"DEL\",\n  \"OnwardDate\": \"2025-08-21\",\n  \"ADT\": 1,\n  \"Cabin\": \"C\"\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}, {"name": "FlightInfo - Specific Flight", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"From\": \"B<PERSON>\",\n  \"To\": \"DEL\",\n  \"OnwardDate\": \"2025-08-21\",\n  \"FlightNo\": \"2986\",\n  \"Provider\": \"AI\",\n  \"ADT\": 1,\n  \"Cabin\": \"Y\"\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}, {"name": "FlightInfo - Different Route (DEL-BOM)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"From\": \"DEL\",\n  \"To\": \"BOM\",\n  \"OnwardDate\": \"2025-08-21\",\n  \"ADT\": 1,\n  \"Cabin\": \"Y\"\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}, {"name": "FlightInfo - Error Test (Invalid Date)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"From\": \"BOM\",\n  \"To\": \"DEL\",\n  \"OnwardDate\": \"invalid-date\",\n  \"ADT\": 1\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}, {"name": "FlightInfo - <PERSON><PERSON><PERSON> Test (Missing Required <PERSON>)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"From\": \"BOM\"\n}"}, "url": {"raw": "{{base_url}}/api/flightinfo/soap", "host": ["{{base_url}}"], "path": ["api", "flightinfo", "soap"]}}, "response": []}]}